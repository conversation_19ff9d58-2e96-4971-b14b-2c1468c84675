import { createClient } from '@supabase/supabase-js'

// Get environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://yekarqanirdkdckimpna.supabase.co'
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc'

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  db: {
    schema: 'mission_fresh'
  },
  auth: {
    persistSession: false
  }
})

async function debugVendors() {
  console.log('🔍 Debugging vendor data...')
  
  try {
    const { data: vendors, error } = await supabase
      .schema('mission_fresh')
      .from('vendors')
      .select('*')
      .order('rating', { ascending: false });

    if (error) {
      console.error('❌ Error fetching vendors:', error)
      return
    }

    console.log(`✅ Found ${vendors.length} vendors`)
    
    if (vendors.length > 0) {
      console.log('\n📊 First vendor structure:')
      console.log(JSON.stringify(vendors[0], null, 2))
      
      console.log('\n📋 All vendors summary:')
      vendors.forEach((vendor, index) => {
        console.log(`${index + 1}. ${vendor.name} - Rating: ${vendor.rating}, Verified: ${vendor.verified}`)
      })
    }

  } catch (err) {
    console.error('❌ Error:', err)
  }
}

debugVendors()
