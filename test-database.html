<!DOCTYPE html>
<html>
<head>
    <title>Database Test</title>
</head>
<body>
    <h1>NRT Products Database Test</h1>
    <div id="results"></div>
    
    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        // Exact same configuration as the app
        const supabaseUrl = 'https://yekarqanirdkdckimpna.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc';
        
        const supabase = createClient(supabaseUrl, supabaseKey, {
            db: { schema: 'mission_fresh' }
        });
        
        const results = document.getElementById('results');
        
        async function testDatabase() {
            results.innerHTML = '<p>Testing database connection...</p>';
            console.log('Testing database connection...');
            
            try {
                // Test 1: Check if we can connect to the table
                console.log('Testing nrt_products table...');
                const { data: nrtData, error: nrtError } = await supabase
                    .from('nrt_products')
                    .select('id, name')
                    .limit(1);
                
                console.log('NRT Products result:', { nrtData, nrtError });
                
                // Test 2: Check smokeless_products table for comparison
                console.log('Testing smokeless_products table...');
                const { data, error } = await supabase
                    .from('smokeless_products')
                    .select('*')
                    .limit(3);
                
                console.log('Query result:', { data, error });
                results.innerHTML += `<p><strong>Query result:</strong></p>`;
                results.innerHTML += `<p>Data: ${JSON.stringify(data)}</p>`;
                results.innerHTML += `<p>Error: ${JSON.stringify(error)}</p>`;
                
                if (error) {
                    results.innerHTML += `<p style="color: red;"><strong>Error details:</strong></p>`;
                    results.innerHTML += `<p>Code: ${error.code}</p>`;
                    results.innerHTML += `<p>Message: ${error.message}</p>`;
                    results.innerHTML += `<p>Details: ${error.details}</p>`;
                    results.innerHTML += `<p>Hint: ${error.hint}</p>`;
                }
                
                // Test 2: Try to list tables in the schema
                if (error && error.code === '42P01') {
                    results.innerHTML += `<p style="color: orange;">Table might not exist. Trying to list schema tables...</p>`;
                    
                    const { data: tables, error: tableError } = await supabase
                        .rpc('exec_sql', { sql: "SELECT table_name FROM information_schema.tables WHERE table_schema = 'mission_fresh';" });
                    
                    console.log('Tables result:', { tables, tableError });
                    results.innerHTML += `<p>Available tables: ${JSON.stringify(tables)}</p>`;
                    results.innerHTML += `<p>Table query error: ${JSON.stringify(tableError)}</p>`;
                }
                
            } catch (err) {
                console.error('Test failed:', err);
                results.innerHTML += `<p style="color: red;"><strong>Test failed:</strong> ${err.message}</p>`;
            }
        }
        
        testDatabase();
    </script>
</body>
</html>
