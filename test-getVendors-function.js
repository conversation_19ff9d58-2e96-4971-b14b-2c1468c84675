import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://yekarqanirdkdckimpna.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc';

const supabase = createClient(supabaseUrl, supabaseKey, {
  db: { schema: 'mission_fresh' }
});

// Replicate the exact getVendors function from supabase.ts
const getVendors = async () => {
  try {
    console.log('getVendors: Fetching vendors from database...');
    
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('vendors')
      .select('*')
      .order('rating', { ascending: false });

    if (error) {
      console.error('getVendors: Database error:', error);
      return [];
    }

    console.log('getVendors: Successfully fetched', data?.length || 0, 'vendors');
    return data?.map(vendor => ({
      id: vendor.id,
      name: vendor.name,
      website: vendor.website,
      logo_url: vendor.logo_url,
      description: vendor.description,
      rating: vendor.rating,
      review_count: vendor.review_count,
      shipping_info: vendor.shipping_info,
      min_order: vendor.min_order,
      delivery_time: vendor.delivery_time,
      coverage_areas: vendor.coverage_areas,
      specialties: vendor.specialties,
      verified: vendor.verified,
      affiliate_commission: vendor.affiliate_commission,
      created_at: vendor.created_at,
      updated_at: vendor.updated_at
    })) || [];
  } catch (error) {
    console.error('getVendors: Error:', error);
    return [];
  }
};

async function testGetVendorsFunction() {
  console.log('🧪 Testing getVendors function exactly as used in SimpleVendorTest...');
  
  try {
    const vendors = await getVendors();
    
    console.log(`✅ getVendors returned ${vendors.length} vendors`);
    
    if (vendors.length > 0) {
      console.log('📋 Vendor details:');
      vendors.forEach((vendor, index) => {
        console.log(`  ${index + 1}. ${vendor.name}`);
        console.log(`     - Rating: ${vendor.rating}`);
        console.log(`     - Verified: ${vendor.verified}`);
        console.log(`     - Description: ${vendor.description?.substring(0, 50)}...`);
        console.log('');
      });
      
      console.log('🎉 ERROR 14 should be RESOLVED - vendors are loading correctly!');
    } else {
      console.log('❌ ERROR 14 confirmed - getVendors returns empty array');
      console.log('🔧 Need to investigate why getVendors returns empty despite database having records');
    }
    
  } catch (error) {
    console.error('❌ Error testing getVendors function:', error);
  }
}

// Run the test
testGetVendorsFunction().catch(console.error);
