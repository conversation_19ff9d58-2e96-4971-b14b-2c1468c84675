import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://yekarqanirdkdckimpna.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  db: {
    schema: 'mission_fresh'
  }
});

async function createNewsletterTable() {
  console.log('🚀 Creating newsletter_signups table in mission_fresh schema...');
  
  try {
    // First, let's create the table using SQL
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS mission_fresh.newsletter_signups (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          email VARCHAR(255) UNIQUE NOT NULL,
          subscribed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'unsubscribed', 'bounced')),
          source VARCHAR(50) DEFAULT 'landing_page',
          preferences JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create index on email for fast lookups
        CREATE INDEX IF NOT EXISTS idx_newsletter_signups_email ON mission_fresh.newsletter_signups(email);
        
        -- Create index on status for filtering
        CREATE INDEX IF NOT EXISTS idx_newsletter_signups_status ON mission_fresh.newsletter_signups(status);
        
        -- Create updated_at trigger
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ language 'plpgsql';
        
        DROP TRIGGER IF EXISTS update_newsletter_signups_updated_at ON mission_fresh.newsletter_signups;
        CREATE TRIGGER update_newsletter_signups_updated_at 
          BEFORE UPDATE ON mission_fresh.newsletter_signups 
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
      `
    });

    if (error) {
      console.error('❌ SQL execution failed:', error);
      
      // If rpc method doesn't work, try direct table creation
      console.log('🔄 Trying alternative table creation method...');
      
      const { data: createData, error: createError } = await supabase
        .from('information_schema.tables')
        .insert({
          table_name: 'newsletter_signups',
          table_schema: 'mission_fresh'
        });
        
      if (createError) {
        console.error('❌ Alternative method failed:', createError);
        console.log('ℹ️ You may need to create this table manually in Supabase dashboard:');
        console.log(`
CREATE TABLE mission_fresh.newsletter_signups (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  subscribed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'unsubscribed', 'bounced')),
  source VARCHAR(50) DEFAULT 'landing_page',
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_newsletter_signups_email ON mission_fresh.newsletter_signups(email);
CREATE INDEX idx_newsletter_signups_status ON mission_fresh.newsletter_signups(status);
        `);
      } else {
        console.log('✅ Table creation initiated via alternative method');
      }
    } else {
      console.log('✅ Newsletter table created successfully!');
    }

    // Test the table by inserting a test record
    console.log('🧪 Testing table with sample insert...');
    const { data: testData, error: testError } = await supabase
      .from('newsletter_signups')
      .insert([
        {
          email: '<EMAIL>',
          source: 'test_script',
          status: 'active'
        }
      ])
      .select();

    if (testError) {
      console.error('❌ Test insert failed:', testError);
      console.log('ℹ️ Table may need to be created manually in Supabase dashboard');
    } else {
      console.log('✅ Test insert successful:', testData);
      
      // Clean up test record
      await supabase
        .from('newsletter_signups')
        .delete()
        .eq('email', '<EMAIL>');
      
      console.log('🧹 Test record cleaned up');
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the creation
createNewsletterTable();
