import puppeteer from 'puppeteer';

async function testAuthEmailField() {
  console.log('🔍 Testing Authentication Email Field React State...');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1200, height: 800 }
  });
  
  try {
    const page = await browser.newPage();
    
    // Navigate to the app
    console.log('📱 Navigating to localhost:5002...');
    await page.goto('http://localhost:5002', { waitUntil: 'networkidle2' });
    
    // Wait for page to load
    await page.waitForTimeout(2000);
    
    // Look for Sign In button and click it
    console.log('🔍 Looking for Sign In button...');
    const signInButton = await page.$('button:contains("Sign In"), a:contains("Sign In"), [data-testid="sign-in"]');
    
    if (!signInButton) {
      // Try alternative selectors
      const buttons = await page.$$('button');
      console.log(`Found ${buttons.length} buttons on page`);
      
      for (let i = 0; i < buttons.length; i++) {
        const buttonText = await buttons[i].evaluate(el => el.textContent);
        console.log(`Button ${i}: "${buttonText}"`);
        
        if (buttonText && buttonText.toLowerCase().includes('sign')) {
          console.log('✅ Found Sign In button, clicking...');
          await buttons[i].click();
          break;
        }
      }
    } else {
      await signInButton.click();
    }
    
    // Wait for modal to appear
    await page.waitForTimeout(1000);
    
    // Look for email input field
    console.log('🔍 Looking for email input field...');
    const emailInput = await page.$('input[type="email"], input[id="email"], input[name="email"]');
    
    if (!emailInput) {
      console.log('❌ Email input field not found');
      return;
    }
    
    console.log('✅ Email input field found');
    
    // Test email field state synchronization
    console.log('🧪 Testing email field React state synchronization...');
    
    // Clear any existing value
    await emailInput.click({ clickCount: 3 });
    await emailInput.press('Backspace');
    
    // Type test email
    const testEmail = '<EMAIL>';
    console.log(`⌨️  Typing email: ${testEmail}`);
    await emailInput.type(testEmail, { delay: 100 });
    
    // Wait a moment for React state to update
    await page.waitForTimeout(500);
    
    // Get the current value from the input
    const inputValue = await emailInput.evaluate(el => el.value);
    console.log(`📝 Input value: "${inputValue}"`);
    
    // Check if the value matches what we typed
    if (inputValue === testEmail) {
      console.log('✅ SUCCESS: Email field React state is working correctly');
      console.log('✅ Email input value matches typed text');
      
      // Test form submission readiness
      const passwordInput = await page.$('input[type="password"]');
      if (passwordInput) {
        await passwordInput.type('TestPass123', { delay: 100 });
        await page.waitForTimeout(500);
        
        const submitButton = await page.$('button[type="submit"]');
        if (submitButton) {
          const isDisabled = await submitButton.evaluate(el => el.disabled);
          console.log(`🔘 Submit button disabled: ${isDisabled}`);
          
          if (!isDisabled) {
            console.log('✅ Form is ready for submission - email state working');
          }
        }
      }
      
    } else {
      console.log('❌ FAILURE: Email field React state bug confirmed');
      console.log(`❌ Expected: "${testEmail}", Got: "${inputValue}"`);
    }
    
    // Take screenshot for verification
    await page.screenshot({ path: 'auth-email-test.png', fullPage: true });
    console.log('📸 Screenshot saved as auth-email-test.png');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await browser.close();
  }
}

// Run the test
testAuthEmailField().catch(console.error);
