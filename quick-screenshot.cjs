const puppeteer = require('puppeteer');

async function takeQuickScreenshot() {
  console.log('📸 Taking quick homepage screenshot...');
  
  try {
    const browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1200, height: 800 }
    });
    
    const page = await browser.newPage();
    await page.setViewport({ width: 1200, height: 800 });
    
    await page.goto('http://localhost:5002', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    // Wait for content to load
    await new <PERSON>(resolve => setTimeout(resolve, 3000));
    
    // Take screenshot
    await page.screenshot({ 
      path: 'homepage-inspection-current.png',
      fullPage: true 
    });
    
    console.log('✅ Screenshot saved: homepage-inspection-current.png');
    
    await browser.close();
    
  } catch (error) {
    console.error('❌ Screenshot error:', error.message);
  }
}

takeQuickScreenshot();
