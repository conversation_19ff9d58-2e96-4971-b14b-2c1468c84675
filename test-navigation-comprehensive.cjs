const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  try {
    console.log('🧭 COMPREHENSIVE NAVIGATION TESTING - localhost:5002');
    await page.goto('http://localhost:5002', { waitUntil: 'domcontentloaded', timeout: 30000 });
    
    // Test actual navigation items found in LandingPage
    const navigationItems = [
      { name: 'Store Locator', selector: 'a[href="/stores"]', expectedUrl: '/stores' },
      { name: 'Online Vendors', selector: 'a[href="/vendors"]', expectedUrl: '/vendors' },
      { name: 'Price Compare', selector: 'a[href="/price-compare"]', expectedUrl: '/price-compare' },
      { name: 'Reviews', selector: 'a[href="/reviews"]', expectedUrl: '/reviews' },
      { name: 'Deals', selector: 'a[href="/deals"]', expectedUrl: '/deals' },
      { name: 'Community Support', selector: 'a[href="/community"]', expectedUrl: '/community' },
      { name: 'Progress Tracking', selector: 'a[href="/progress"]', expectedUrl: '/progress' }
    ];
    
    console.log('\\n📋 Testing Main Desktop Navigation Items:');
    
    for (const item of navigationItems) {
      try {
        console.log(`\\n🔍 Testing: ${item.name}`);
        
        // Go back to homepage first
        await page.goto('http://localhost:5002', { waitUntil: 'domcontentloaded' });
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Look for the navigation link
        const navLink = await page.$(item.selector);
        
        if (navLink) {
          console.log(`✅ Navigation link found: ${item.name}`);
          
          // Click the navigation link
          await navLink.click();
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          // Check current URL
          const currentUrl = page.url();
          const pathname = new URL(currentUrl).pathname;
          
          if (pathname === item.expectedUrl) {
            console.log(`✅ Navigation successful: ${pathname}`);
            
            // Check for page content
            const pageContent = await page.evaluate(() => {
              const body = document.body;
              return {
                hasContent: body.innerText.length > 100,
                hasError: body.innerText.toLowerCase().includes('error'),
                hasLoading: body.innerText.toLowerCase().includes('loading'),
                title: document.title
              };
            });
            
            if (pageContent.hasError) {
              console.log(`❌ Page shows error content`);
            } else if (pageContent.hasLoading) {
              console.log(`⚠️ Page stuck in loading state`);
            } else if (pageContent.hasContent) {
              console.log(`✅ Page loaded with content (${pageContent.title})`);
            } else {
              console.log(`⚠️ Page loaded but minimal content`);
            }
            
            // Check console errors
            const logs = await page.evaluate(() => {
              return window.console.errors || [];
            });
            
            if (logs.length > 0) {
              console.log(`⚠️ Console errors detected: ${logs.length}`);
            }
            
          } else {
            console.log(`❌ Navigation failed: Expected ${item.expectedUrl}, got ${pathname}`);
          }
          
        } else {
          console.log(`❌ Navigation link not found: ${item.name}`);
        }
        
      } catch (error) {
        console.log(`❌ Error testing ${item.name}: ${error.message}`);
      }
    }
    
    console.log('\\n🧭 NAVIGATION TESTING COMPLETE');
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  
  await browser.close();
})();
