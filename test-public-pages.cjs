const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  try {
    console.log('📄 PUBLIC PAGES COMPREHENSIVE INSPECTION - localhost:5002');
    
    // Test public pages
    const publicPages = [
      { name: 'Landing Page', url: '/', sections: ['Hero', 'Features', 'Testimonials', 'Footer'] },
      { name: 'About Page', url: '/about', sections: ['Content'] },
      { name: 'Contact Page', url: '/contact', sections: ['Content'] },
      { name: 'Terms of Service', url: '/terms', sections: ['Content'] },
      { name: 'Privacy Policy', url: '/privacy', sections: ['Content'] }
    ];
    
    console.log('\\n📋 Testing Public Pages:');
    
    for (const pageInfo of publicPages) {
      try {
        console.log(`\\n🔍 Testing: ${pageInfo.name} (${pageInfo.url})`);
        
        await page.goto(`http://localhost:5002${pageInfo.url}`, { waitUntil: 'domcontentloaded', timeout: 15000 });
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Check page content
        const pageAnalysis = await page.evaluate(() => {
          const body = document.body;
          const title = document.title;
          const content = body.innerText.toLowerCase();
          
          // Check for specific sections
          const hasHero = content.includes('hero') || content.includes('welcome') || content.includes('discover');
          const hasFeatures = content.includes('features') || content.includes('benefits') || content.includes('why');
          const hasTestimonials = content.includes('testimonial') || content.includes('review') || content.includes('success');
          const hasFooter = content.includes('footer') || content.includes('copyright') || content.includes('privacy');
          
          return {
            title: title,
            hasContent: body.innerText.length > 100,
            hasError: content.includes('error') || content.includes('not found'),
            hasLoading: content.includes('loading'),
            contentLength: body.innerText.length,
            sections: {
              hero: hasHero,
              features: hasFeatures,
              testimonials: hasTestimonials,
              footer: hasFooter
            },
            hasAuthModal: !!document.querySelector('[class*="modal"], [class*="dialog"]'),
            hasSignInButton: content.includes('sign in') || content.includes('login'),
            hasSignUpButton: content.includes('sign up') || content.includes('register')
          };
        });
        
        console.log(`📄 Page Title: ${pageAnalysis.title}`);
        console.log(`📊 Content Length: ${pageAnalysis.contentLength} characters`);
        
        if (pageAnalysis.hasError) {
          console.log(`❌ Page shows error content`);
        } else if (pageAnalysis.hasLoading) {
          console.log(`⚠️ Page stuck in loading state`);
        } else if (pageAnalysis.hasContent) {
          console.log(`✅ Page loaded with content`);
          
          // Check sections for Landing Page
          if (pageInfo.name === 'Landing Page') {
            console.log(`  📍 Hero Section: ${pageAnalysis.sections.hero ? '✅' : '❌'}`);
            console.log(`  🎯 Features Section: ${pageAnalysis.sections.features ? '✅' : '❌'}`);
            console.log(`  💬 Testimonials Section: ${pageAnalysis.sections.testimonials ? '✅' : '❌'}`);
            console.log(`  🦶 Footer Section: ${pageAnalysis.sections.footer ? '✅' : '❌'}`);
            console.log(`  🔐 Sign In Button: ${pageAnalysis.hasSignInButton ? '✅' : '❌'}`);
            console.log(`  📝 Sign Up Button: ${pageAnalysis.hasSignUpButton ? '✅' : '❌'}`);
          }
        } else {
          console.log(`⚠️ Page loaded but minimal content`);
        }
        
        // Take screenshot
        const filename = `public-${pageInfo.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}.png`;
        await page.screenshot({ path: filename, fullPage: false });
        console.log(`📸 Screenshot saved: ${filename}`);
        
      } catch (error) {
        console.log(`❌ Error testing ${pageInfo.name}: ${error.message}`);
      }
    }
    
    // Test authentication modals
    console.log('\\n🔐 Testing Authentication Modals:');
    
    try {
      await page.goto('http://localhost:5002', { waitUntil: 'domcontentloaded' });
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Look for Sign In button
      const signInButton = await page.$('button:has-text("Sign In"), button[class*="signin"], button[class*="sign-in"]');
      if (signInButton) {
        console.log('✅ Sign In button found');
        await signInButton.click();
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const modalVisible = await page.evaluate(() => {
          const modals = document.querySelectorAll('[class*="modal"], [class*="dialog"], [role="dialog"]');
          return Array.from(modals).some(modal => 
            modal.offsetParent !== null && 
            modal.style.display !== 'none'
          );
        });
        
        if (modalVisible) {
          console.log('✅ Sign In modal opens successfully');
          await page.screenshot({ path: 'auth-signin-modal.png', fullPage: false });
          console.log('📸 Screenshot saved: auth-signin-modal.png');
        } else {
          console.log('❌ Sign In modal not visible after click');
        }
      } else {
        console.log('❌ Sign In button not found');
      }
      
    } catch (error) {
      console.log(`❌ Error testing authentication: ${error.message}`);
    }
    
    console.log('\\n📄 PUBLIC PAGES INSPECTION COMPLETE');
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  
  await browser.close();
})();
