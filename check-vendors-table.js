import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://yekarqanirdkdckimpna.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc';

const supabase = createClient(supabaseUrl, supabaseKey, {
  db: { schema: 'mission_fresh' }
});

async function checkVendorsTable() {
  console.log('🔍 Checking vendors table in mission_fresh schema...');
  
  try {
    // Check if vendors table exists and has data
    const { data: vendors, error } = await supabase
      .from('vendors')
      .select('*')
      .limit(10);
    
    if (error) {
      console.error('❌ Error querying vendors table:', error.message);
      console.log('🔧 This likely means the table doesn\'t exist or has RLS issues');
      return;
    }
    
    console.log(`✅ Vendors table exists with ${vendors?.length || 0} records`);
    
    if (vendors && vendors.length > 0) {
      console.log('📋 Sample vendor data:');
      vendors.forEach((vendor, index) => {
        console.log(`  ${index + 1}. ${vendor.name} (Rating: ${vendor.rating || 'N/A'})`);
      });
    } else {
      console.log('📭 Vendors table is empty - this explains ERROR 14');
      console.log('🚀 Need to populate vendors table with sample data');
      
      // Try to insert sample vendors
      await populateVendorsTable();
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

async function populateVendorsTable() {
  console.log('🚀 Populating vendors table with sample data...');
  
  const sampleVendors = [
    {
      id: 'vendor-nrt-direct',
      name: 'NRT Direct',
      website: 'https://nrtdirect.com',
      logo_url: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=150',
      description: 'Leading online retailer of NRT products with fast shipping and competitive prices. Specializing in nicotine gum, patches, and lozenges.',
      rating: 4.8,
      review_count: 2847,
      shipping_info: 'Free shipping on orders over $50. Standard delivery 3-5 business days.',
      min_order: 25.00,
      delivery_time: '3-5 business days',
      coverage_areas: ['US', 'Canada'],
      specialties: ['Nicotine Gum', 'Nicotine Patches', 'Lozenges'],
      verified: true,
      affiliate_commission: 0.08
    },
    {
      id: 'vendor-quit-now',
      name: 'QuitNow Pharmacy',
      website: 'https://quitnowpharmacy.com',
      logo_url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=150',
      description: 'Specialized pharmacy focusing on smoking cessation products and counseling services. Expert guidance for your quit journey.',
      rating: 4.6,
      review_count: 1923,
      shipping_info: 'Express shipping available. Free consultation with pharmacist.',
      min_order: 30.00,
      delivery_time: '2-4 business days',
      coverage_areas: ['US'],
      specialties: ['Prescription NRT', 'Counseling', 'Nicotine Inhalers'],
      verified: true,
      affiliate_commission: 0.12
    },
    {
      id: 'vendor-smoke-free',
      name: 'SmokeFree Solutions',
      website: 'https://smokefreesolutions.com',
      logo_url: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=150',
      description: 'Comprehensive smoking cessation support with premium NRT products and personalized quit plans.',
      rating: 4.7,
      review_count: 3156,
      shipping_info: 'Same-day shipping in major cities. 30-day money-back guarantee.',
      min_order: 20.00,
      delivery_time: '1-3 business days',
      coverage_areas: ['US', 'Canada', 'UK'],
      specialties: ['Nicotine Patches', 'Behavioral Support', 'Quit Kits'],
      verified: true,
      affiliate_commission: 0.10
    }
  ];
  
  try {
    const { data, error } = await supabase
      .from('vendors')
      .insert(sampleVendors)
      .select();
    
    if (error) {
      console.error('❌ Error inserting vendors:', error.message);
      console.log('🔧 This might be due to RLS policies or table permissions');
      return;
    }
    
    console.log(`✅ Successfully inserted ${data?.length || 0} vendors`);
    console.log('🎉 ERROR 14 should now be resolved!');
    
  } catch (error) {
    console.error('❌ Unexpected error inserting vendors:', error);
  }
}

// Run the check
checkVendorsTable().catch(console.error);
