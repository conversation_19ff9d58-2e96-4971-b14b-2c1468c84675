import { createClient } from '@supabase/supabase-js';

// Supabase connection setup for mission_fresh schema
const supabaseUrl = 'https://yekarqanirdkdckimpna.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc';

const supabase = createClient(supabaseUrl, supabaseKey, {
  db: { schema: 'mission_fresh' }
});

// FDA-approved NRT products to insert
const nrtProducts = [
  {
    name: 'Nicorette Gum Original 2mg',
    brand: 'Nicorette',
    category: 'Gum',
    description: 'FDA-approved nicotine gum for smoking cessation',
    dosage: '2mg',
    price: 45.99,
    rating: 4.2,
    reviews: 1250,
    pros: ['Easy to use', 'Reduces cravings', 'Available OTC'],
    cons: ['Jaw soreness', 'Taste issues'],
    effectiveness: 85,
    fda_approved: true
  },
  {
    name: 'Nicorette Gum Original 4mg',
    brand: 'Nicorette',
    category: 'Gum',
    description: 'FDA-approved nicotine gum for heavy smokers',
    dosage: '4mg',
    price: 52.19,
    rating: 4.3,
    reviews: 980,
    pros: ['Higher strength', 'Effective for heavy smokers'],
    cons: ['Strong taste', 'Side effects'],
    effectiveness: 88,
    fda_approved: true
  },
  {
    name: 'NicoDerm CQ Clear Patch 21mg',
    brand: 'NicoDerm CQ',
    category: 'Patch',
    description: 'FDA-approved nicotine patch - Step 1',
    dosage: '21mg',
    price: 83.61,
    rating: 4.5,
    reviews: 2100,
    pros: ['24-hour relief', 'Discreet', 'Steady nicotine'],
    cons: ['Skin irritation', 'Sleep issues'],
    effectiveness: 92,
    fda_approved: true
  },
  {
    name: 'NicoDerm CQ Clear Patch 14mg',
    brand: 'NicoDerm CQ',
    category: 'Patch',
    description: 'FDA-approved nicotine patch - Step 2',
    dosage: '14mg',
    price: 75.34,
    rating: 4.4,
    reviews: 1800,
    pros: ['Reduced strength', 'Good for step-down'],
    cons: ['May not be strong enough'],
    effectiveness: 89,
    fda_approved: true
  },
  {
    name: 'Nicorette Lozenge 2mg',
    brand: 'Nicorette',
    category: 'Lozenge',
    description: 'FDA-approved nicotine lozenge',
    dosage: '2mg',
    price: 48.75,
    rating: 4.1,
    reviews: 850,
    pros: ['Convenient', 'Portable', 'No chewing'],
    cons: ['Dissolves slowly', 'Throat irritation'],
    effectiveness: 83,
    fda_approved: true
  }
];

async function insertNRTProducts() {
  try {
    console.log('🚀 Inserting FDA-approved NRT products...');

    // Insert the products
    const { data, error } = await supabase
      .from('nrt_products')
      .insert(nrtProducts)
      .select();

    if (error) {
      console.error('❌ Error inserting NRT products:', error);
      return;
    }

    console.log('✅ Successfully inserted', data.length, 'FDA-approved NRT products');
    console.log('Products inserted:', data.map(p => `${p.name} (${p.brand})`));

    // Verify the data
    const { data: allProducts, error: queryError } = await supabase
      .from('nrt_products')
      .select('id, name, brand, category, rating')
      .order('name');

    if (queryError) {
      console.error('❌ Error querying products:', queryError);
      return;
    }

    console.log('📊 Total products in database:', allProducts.length);
    allProducts.forEach(product => {
      console.log(`- ${product.name} (${product.brand}) - ${product.category} - Rating: ${product.rating}`);
    });

  } catch (err) {
    console.error('💥 Script failed:', err);
  }
}

insertNRTProducts();
