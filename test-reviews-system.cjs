const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  try {
    console.log('⭐ REVIEWS AND RATINGS SYSTEM TESTING - localhost:5002');
    
    // Test reviews-related pages
    const reviewsPages = [
      { name: 'Reviews Main Page', url: '/reviews' },
      { name: 'NRT Directory (with reviews)', url: '/nrt' },
      { name: 'Product Detail (with reviews)', url: '/product/1' },
      { name: 'Vendor Detail (with reviews)', url: '/vendor/1' }
    ];
    
    console.log('\\n📋 Testing Reviews System:');
    
    for (const pageInfo of reviewsPages) {
      try {
        console.log(`\\n🔍 Testing: ${pageInfo.name} (${pageInfo.url})`);
        
        await page.goto(`http://localhost:5002${pageInfo.url}`, { waitUntil: 'domcontentloaded', timeout: 15000 });
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Check page content and reviews
        const reviewsAnalysis = await page.evaluate(() => {
          const body = document.body;
          const content = body.innerText.toLowerCase();
          
          // Look for review-related elements
          const hasReviews = content.includes('review') || content.includes('rating') || content.includes('star');
          const hasReviewForm = content.includes('write review') || content.includes('add review') || content.includes('submit review');
          const hasRatings = content.includes('rating') || content.includes('star') || content.includes('⭐');
          const hasReviewList = content.includes('reviews') && (content.includes('user') || content.includes('customer'));
          
          // Check for hardcoded review data
          const hasHardcodedReviews = content.includes('john doe') || content.includes('jane smith') || content.includes('test user');
          
          // Check for authentication requirements
          const hasAuthRequired = content.includes('authentication required') || content.includes('sign in');
          const hasError = content.includes('error') || content.includes('not found');
          const hasLoading = content.includes('loading');
          
          // Count review elements
          const reviewElements = document.querySelectorAll('[class*="review"], [class*="rating"], [data-testid*="review"]');
          const starElements = document.querySelectorAll('[class*="star"], .rating');
          
          return {
            hasContent: body.innerText.length > 100,
            contentLength: body.innerText.length,
            hasReviews: hasReviews,
            hasReviewForm: hasReviewForm,
            hasRatings: hasRatings,
            hasReviewList: hasReviewList,
            hasHardcodedReviews: hasHardcodedReviews,
            hasAuthRequired: hasAuthRequired,
            hasError: hasError,
            hasLoading: hasLoading,
            reviewElementsCount: reviewElements.length,
            starElementsCount: starElements.length
          };
        });
        
        console.log(`📊 Content Length: ${reviewsAnalysis.contentLength} characters`);
        
        if (reviewsAnalysis.hasError) {
          console.log(`❌ Page shows error content`);
        } else if (reviewsAnalysis.hasAuthRequired) {
          console.log(`🔒 Authentication required`);
        } else if (reviewsAnalysis.hasLoading) {
          console.log(`⚠️ Page stuck in loading state`);
        } else if (reviewsAnalysis.hasContent) {
          console.log(`✅ Page loaded with content`);
          
          // Review system analysis
          console.log(`  ⭐ Has Reviews: ${reviewsAnalysis.hasReviews ? '✅' : '❌'}`);
          console.log(`  📝 Has Review Form: ${reviewsAnalysis.hasReviewForm ? '✅' : '❌'}`);
          console.log(`  🌟 Has Ratings: ${reviewsAnalysis.hasRatings ? '✅' : '❌'}`);
          console.log(`  📋 Has Review List: ${reviewsAnalysis.hasReviewList ? '✅' : '❌'}`);
          console.log(`  🚫 Hardcoded Reviews: ${reviewsAnalysis.hasHardcodedReviews ? '❌ VIOLATION' : '✅ Clean'}`);
          console.log(`  🔢 Review Elements: ${reviewsAnalysis.reviewElementsCount}`);
          console.log(`  ⭐ Star Elements: ${reviewsAnalysis.starElementsCount}`);
          
        } else {
          console.log(`⚠️ Page loaded but minimal content`);
        }
        
        // Take screenshot
        const filename = `reviews-${pageInfo.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}.png`;
        await page.screenshot({ path: filename, fullPage: false });
        console.log(`📸 Screenshot saved: ${filename}`);
        
      } catch (error) {
        console.log(`❌ Error testing ${pageInfo.name}: ${error.message}`);
      }
    }
    
    // Test review submission functionality
    console.log('\\n📝 Testing Review Submission:');
    
    try {
      await page.goto('http://localhost:5002/reviews', { waitUntil: 'domcontentloaded' });
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Look for review submission elements
      const reviewFormCheck = await page.evaluate(() => {
        const forms = document.querySelectorAll('form');
        const textareas = document.querySelectorAll('textarea');
        const submitButtons = Array.from(document.querySelectorAll('button')).filter(btn => 
          btn.textContent.toLowerCase().includes('submit') || 
          btn.textContent.toLowerCase().includes('review') ||
          btn.textContent.toLowerCase().includes('post')
        );
        
        return {
          formsCount: forms.length,
          textareasCount: textareas.length,
          submitButtonsCount: submitButtons.length,
          submitButtonsText: submitButtons.map(btn => btn.textContent.trim())
        };
      });
      
      console.log(`📋 Forms found: ${reviewFormCheck.formsCount}`);
      console.log(`📝 Textareas found: ${reviewFormCheck.textareasCount}`);
      console.log(`🔘 Submit buttons: ${reviewFormCheck.submitButtonsCount} (${reviewFormCheck.submitButtonsText.join(', ')})`);
      
      if (reviewFormCheck.formsCount > 0 || reviewFormCheck.textareasCount > 0) {
        console.log('✅ Review submission form elements detected');
      } else {
        console.log('❌ No review submission form found');
      }
      
    } catch (error) {
      console.log(`❌ Error testing review submission: ${error.message}`);
    }
    
    console.log('\\n⭐ REVIEWS AND RATINGS SYSTEM TESTING COMPLETE');
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  
  await browser.close();
})();
