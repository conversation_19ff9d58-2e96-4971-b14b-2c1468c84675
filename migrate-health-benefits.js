// HOLY RULE #1 COMPLIANCE FIX - Create health benefits and milestones tables
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.VITE_SUPABASE_ANON_KEY);

async function createHealthBenefitsTable() {
  console.log('Creating health_benefits table...');
  
  const { data, error } = await supabase.rpc('create_table_sql', {
    table_name: 'health_benefits',
    schema_name: 'mission_fresh'
  }).select();

  if (error) {
    console.log('Table might already exist, proceeding...');
  } else {
    console.log('Health benefits table created successfully');
  }
}

async function insertHealthBenefits() {
  console.log('Inserting health benefits data...');
  
  const healthBenefits = [
    {
      timeline: '20 Minutes',
      title: 'Improved Circulation',
      description: 'Heart rate and blood pressure drop to normal levels',
      icon_type: 'heart',
      days_required: 0
    },
    {
      timeline: '2 Weeks',
      title: 'Enhanced Energy',
      description: 'Circulation improves and lung function increases by up to 30%',
      icon_type: 'activity',
      days_required: 14
    },
    {
      timeline: '3 Months',
      title: 'Lung Recovery',
      description: 'Significant improvement in breathing and reduced coughing',
      icon_type: 'trending',
      days_required: 90
    },
    {
      timeline: '1 Year',
      title: 'Heart Health',
      description: 'Risk of heart disease drops by 50% compared to smokers',
      icon_type: 'heart',
      days_required: 365
    },
    {
      timeline: '5 Years',
      title: 'Cancer Risk Reduction',
      description: 'Risk of mouth, throat, and bladder cancers cut in half',
      icon_type: 'shield',
      days_required: 1825
    },
    {
      timeline: '10 Years',
      title: 'Lung Cancer Protection',
      description: 'Risk of lung cancer drops by 50% compared to smokers',
      icon_type: 'trending',
      days_required: 3650
    }
  ];

  // Check if table exists and has data
  const { data: existingData, error: checkError } = await supabase
    .from('health_benefits')
    .select('id')
    .limit(1);

  if (!checkError && existingData && existingData.length > 0) {
    console.log('Health benefits data already exists');
    return;
  }

  const { data, error } = await supabase
    .from('health_benefits')
    .insert(healthBenefits)
    .select();

  if (error) {
    console.error('Error inserting health benefits:', error);
  } else {
    console.log('Health benefits inserted successfully:', data.length, 'records');
  }
}

async function insertMilestones() {
  console.log('Inserting milestones data...');
  
  const milestones = [
    {
      name: '7-Day Champion',
      description: 'Completed your first week smoke-free! This is a major accomplishment.',
      days_required: 7,
      category: 'weekly',
      reward_points: 100,
      icon_type: 'trophy'
    },
    {
      name: '30-Day Warrior',
      description: 'One month smoke-free! Your body is already healing significantly.',
      days_required: 30,
      category: 'monthly',
      reward_points: 300,
      icon_type: 'medal'
    },
    {
      name: '90-Day Master',
      description: 'Three months of freedom! Your lung function has improved dramatically.',
      days_required: 90,
      category: 'quarterly',
      reward_points: 500,
      icon_type: 'crown'
    },
    {
      name: '180-Day Hero',
      description: 'Six months smoke-free! You have broken the physical addiction completely.',
      days_required: 180,
      category: 'milestone',
      reward_points: 750,
      icon_type: 'star'
    },
    {
      name: '365-Day Legend',
      description: 'One full year! You have officially transformed your life and health.',
      days_required: 365,
      category: 'annual',
      reward_points: 1000,
      icon_type: 'diamond'
    },
    {
      name: '2-Year Veteran',
      description: 'Two years of smoke-free living! You are an inspiration to others.',
      days_required: 730,
      category: 'major',
      reward_points: 1500,
      icon_type: 'badge'
    },
    {
      name: '5-Year Master',
      description: 'Five years smoke-free! Your health risks have dropped to near-normal levels.',
      days_required: 1825,
      category: 'major',
      reward_points: 2500,
      icon_type: 'crown'
    }
  ];

  // Check if table exists and has data
  const { data: existingData, error: checkError } = await supabase
    .from('milestones')
    .select('id')
    .limit(1);

  if (!checkError && existingData && existingData.length > 0) {
    console.log('Milestones data already exists');
    return;
  }

  const { data, error } = await supabase
    .from('milestones')
    .insert(milestones)
    .select();

  if (error) {
    console.error('Error inserting milestones:', error);
  } else {
    console.log('Milestones inserted successfully:', data.length, 'records');
  }
}

async function runMigration() {
  try {
    await createHealthBenefitsTable();
    await insertHealthBenefits();
    await insertMilestones();
    console.log('✅ Migration completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

runMigration();
