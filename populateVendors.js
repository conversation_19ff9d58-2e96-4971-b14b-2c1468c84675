import { createClient } from '@supabase/supabase-js'
import 'dotenv/config'

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
)

const vendorData = [
  {
    id: 'vendor-001',
    name: 'CVS Pharmacy Online',
    description: 'Trusted nationwide pharmacy chain with comprehensive NRT selection and fast delivery.',
    website_url: 'https://www.cvs.com',
    logo_url: 'https://logos-world.net/wp-content/uploads/2020/11/CVS-Pharmacy-Symbol.png',
    rating: 4.5,
    review_count: 15420,
    shipping_info: 'Free shipping on orders over $35. Same-day delivery available in select areas.',
    delivery_time: '1-3 business days',
    return_policy: '30-day return policy with receipt',
    verified: true,
    accepts_insurance: true,
    prescription_required: false,
    product_categories: ['nicotine_gum', 'nicotine_patch', 'nicotine_lozenge'],
    contact_info: {
      phone: '**************',
      email: '<EMAIL>',
      chat_available: true
    },
    business_hours: 'Mon-Sun: 24/7 online ordering',
    location_coverage: 'Nationwide (US)',
    payment_methods: ['credit_card', 'debit_card', 'paypal', 'apple_pay', 'google_pay'],
    specialties: ['pharmacy', 'health_products', 'wellness'],
    certifications: ['FDA_verified', 'NABP_certified'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'vendor-002',
    name: 'Walgreens Pharmacy',
    description: 'Leading pharmacy retailer offering comprehensive smoking cessation products with expert consultation.',
    website_url: 'https://www.walgreens.com',
    logo_url: 'https://logos-world.net/wp-content/uploads/2020/11/Walgreens-Logo.png',
    rating: 4.3,
    review_count: 12850,
    shipping_info: 'Free shipping on orders over $35. Express delivery available.',
    delivery_time: '1-3 business days',
    return_policy: '30-day satisfaction guarantee',
    verified: true,
    accepts_insurance: true,
    prescription_required: false,
    product_categories: ['nicotine_gum', 'nicotine_patch', 'nicotine_lozenge'],
    contact_info: {
      phone: '**************',
      email: '<EMAIL>',
      chat_available: true
    },
    business_hours: 'Mon-Sun: 24/7 online ordering',
    location_coverage: 'Nationwide (US)',
    payment_methods: ['credit_card', 'debit_card', 'paypal', 'walgreens_pay'],
    specialties: ['pharmacy', 'health_wellness', 'smoking_cessation'],
    certifications: ['FDA_verified', 'NABP_certified'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'vendor-003',
    name: 'Amazon Pharmacy',
    description: 'Fast, reliable delivery of NRT products with competitive pricing and subscription options.',
    website_url: 'https://pharmacy.amazon.com',
    logo_url: 'https://logos-world.net/wp-content/uploads/2020/04/Amazon-Logo.png',
    rating: 4.2,
    review_count: 28750,
    shipping_info: 'Free Prime shipping. Next-day delivery available for Prime members.',
    delivery_time: '1-2 business days (Prime)',
    return_policy: '30-day return policy',
    verified: true,
    accepts_insurance: false,
    prescription_required: false,
    product_categories: ['nicotine_gum', 'nicotine_patch', 'nicotine_lozenge', 'nicotine_pouches'],
    contact_info: {
      phone: '**************',
      email: '<EMAIL>',
      chat_available: true
    },
    business_hours: 'Mon-Sun: 24/7 online ordering',
    location_coverage: 'Nationwide (US)',
    payment_methods: ['credit_card', 'debit_card', 'amazon_pay', 'gift_cards'],
    specialties: ['online_retail', 'health_products', 'subscription_service'],
    certifications: ['FDA_verified'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'vendor-004',
    name: 'Target Pharmacy',
    description: 'One-stop shopping for NRT products with pharmacy expertise and convenient pickup options.',
    website_url: 'https://www.target.com/c/pharmacy',
    logo_url: 'https://logos-world.net/wp-content/uploads/2020/04/Target-Logo.png',
    rating: 4.1,
    review_count: 9630,
    shipping_info: 'Free shipping on orders over $35. Same-day delivery with Shipt.',
    delivery_time: '1-3 business days',
    return_policy: '90-day return policy',
    verified: true,
    accepts_insurance: true,
    prescription_required: false,
    product_categories: ['nicotine_gum', 'nicotine_patch', 'nicotine_lozenge'],
    contact_info: {
      phone: '**************',
      email: '<EMAIL>',
      chat_available: true
    },
    business_hours: 'Mon-Sun: 24/7 online ordering',
    location_coverage: 'Nationwide (US)',
    payment_methods: ['credit_card', 'debit_card', 'target_redcard', 'paypal'],
    specialties: ['retail_pharmacy', 'health_wellness', 'convenient_pickup'],
    certifications: ['FDA_verified', 'NABP_certified'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'vendor-005',
    name: 'HealthWarehouse',
    description: 'Discount online pharmacy specializing in affordable NRT products with licensed pharmacist support.',
    website_url: 'https://www.healthwarehouse.com',
    logo_url: 'https://www.healthwarehouse.com/assets/images/logo.png',
    rating: 4.4,
    review_count: 5240,
    shipping_info: 'Free shipping on orders over $50. Express shipping available.',
    delivery_time: '2-5 business days',
    return_policy: '30-day return policy for unopened items',
    verified: true,
    accepts_insurance: false,
    prescription_required: false,
    product_categories: ['nicotine_gum', 'nicotine_patch', 'nicotine_lozenge'],
    contact_info: {
      phone: '**************',
      email: '<EMAIL>',
      chat_available: false
    },
    business_hours: 'Mon-Fri: 9 AM - 6 PM EST',
    location_coverage: 'Nationwide (US)',
    payment_methods: ['credit_card', 'debit_card', 'check'],
    specialties: ['discount_pharmacy', 'smoking_cessation', 'bulk_orders'],
    certifications: ['FDA_verified', 'NABP_certified'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'vendor-006',
    name: 'Vitacost',
    description: 'Health and wellness retailer offering natural and traditional NRT solutions at competitive prices.',
    website_url: 'https://www.vitacost.com',
    logo_url: 'https://www.vitacost.com/vweb/assets/images/vitacost-logo.svg',
    rating: 4.0,
    review_count: 3850,
    shipping_info: 'Free shipping on orders over $49. Fast shipping available.',
    delivery_time: '3-7 business days',
    return_policy: '30-day satisfaction guarantee',
    verified: true,
    accepts_insurance: false,
    prescription_required: false,
    product_categories: ['nicotine_gum', 'nicotine_lozenge', 'natural_alternatives'],
    contact_info: {
      phone: '**************',
      email: '<EMAIL>',
      chat_available: true
    },
    business_hours: 'Mon-Sun: 24/7 online ordering',
    location_coverage: 'Nationwide (US)',
    payment_methods: ['credit_card', 'debit_card', 'paypal'],
    specialties: ['health_supplements', 'natural_products', 'wellness'],
    certifications: ['FDA_verified'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'vendor-007',
    name: 'iHerb',
    description: 'Global health products retailer with extensive selection of smoking cessation aids and natural alternatives.',
    website_url: 'https://www.iherb.com',
    logo_url: 'https://s3.images-iherb.com/cms/logos/iherb-logo-white.svg',
    rating: 4.3,
    review_count: 7920,
    shipping_info: 'Free shipping on orders over $40. International shipping available.',
    delivery_time: '2-4 business days',
    return_policy: '90-day money-back guarantee',
    verified: true,
    accepts_insurance: false,
    prescription_required: false,
    product_categories: ['nicotine_gum', 'nicotine_lozenge', 'natural_alternatives', 'supplements'],
    contact_info: {
      phone: '**************',
      email: '<EMAIL>',
      chat_available: true
    },
    business_hours: 'Mon-Sun: 24/7 online ordering',
    location_coverage: 'International',
    payment_methods: ['credit_card', 'debit_card', 'paypal', 'apple_pay'],
    specialties: ['natural_health', 'supplements', 'international_shipping'],
    certifications: ['FDA_verified', 'cGMP_certified'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 'vendor-008',
    name: 'Costco Pharmacy',
    description: 'Membership-based warehouse offering bulk NRT products at wholesale prices with pharmacy services.',
    website_url: 'https://www.costco.com/pharmacy',
    logo_url: 'https://logos-world.net/wp-content/uploads/2020/09/Costco-Logo.png',
    rating: 4.6,
    review_count: 11250,
    shipping_info: 'Free shipping for members on pharmacy orders. 2-day delivery available.',
    delivery_time: '2-5 business days',
    return_policy: '100% satisfaction guarantee',
    verified: true,
    accepts_insurance: true,
    prescription_required: false,
    product_categories: ['nicotine_gum', 'nicotine_patch', 'nicotine_lozenge'],
    contact_info: {
      phone: '**************',
      email: '<EMAIL>',
      chat_available: false
    },
    business_hours: 'Mon-Fri: 10 AM - 8:30 PM, Sat: 9:30 AM - 6 PM, Sun: 10 AM - 6 PM',
    location_coverage: 'Nationwide (US)',
    payment_methods: ['credit_card', 'debit_card', 'costco_card'],
    specialties: ['bulk_purchasing', 'member_discounts', 'pharmacy_services'],
    certifications: ['FDA_verified', 'NABP_certified'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
]

async function populateVendors() {
  try {
    console.log('🚀 Starting vendors database population...')
    
    // Check if vendors table has data
    const { data: existingVendors, error: checkError } = await supabase
      .schema('mission_fresh')
      .from('vendors')
      .select('id')
      .limit(1);
    
    if (checkError) {
      console.error('❌ Error checking existing vendors:', checkError)
      return
    }
    
    if (existingVendors && existingVendors.length > 0) {
      console.log('⚠️  Vendors already exist. Skipping population...')
      
      // Verify the data count
      const { count, error: countError } = await supabase
        .schema('mission_fresh')
        .from('vendors')
        .select('*', { count: 'exact', head: true });
      
      if (!countError) {
        console.log(`✅ Found ${count} existing vendors in database`)
      }
      return
    }
    
    // Insert new vendor data
    console.log(`📦 Inserting ${vendorData.length} vendors...`)
    
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('vendors')
      .insert(vendorData)
      .select()
    
    if (error) {
      console.error('❌ Error inserting vendors:', error)
      return
    }
    
    console.log(`✅ Successfully inserted ${data?.length || vendorData.length} vendors:`)
    vendorData.forEach(vendor => {
      console.log(`   - ${vendor.name} (Rating: ${vendor.rating}/5.0)`)
    })
    
    // Verify the data was inserted
    const { data: verifyData, error: verifyError } = await supabase
      .from('vendors')
      .select('id, name, rating')
    
    if (verifyError) {
      console.error('❌ Error verifying vendors:', verifyError)
      return
    }
    
    console.log(`\n🎉 Database verification complete: ${verifyData?.length || 0} vendors in database`)
    console.log('📊 Average rating:', (verifyData?.reduce((sum, v) => sum + v.rating, 0) / verifyData?.length).toFixed(2))
    
  } catch (error) {
    console.error('💥 Unexpected error:', error)
  }
}

// Run the population script
populateVendors()
