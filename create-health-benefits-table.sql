-- HOLY RULE #1 COMPLIANCE FIX
-- Move hardcoded health benefits data to mission_fresh schema database table

CREATE TABLE mission_fresh.health_benefits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  timeline VARCHAR(50) NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  icon_type VARCHAR(50) NOT NULL,
  days_required INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert actual health benefits data
INSERT INTO mission_fresh.health_benefits (timeline, title, description, icon_type, days_required) VALUES
('20 Minutes', 'Improved Circulation', 'Heart rate and blood pressure drop to normal levels', 'heart', 0),
('2 Weeks', 'Enhanced Energy', 'Circulation improves and lung function increases by up to 30%', 'activity', 14),
('3 Months', 'Lung Recovery', 'Significant improvement in breathing and reduced coughing', 'trending', 90),
('1 Year', 'Heart Health', 'Risk of heart disease drops by 50% compared to smokers', 'heart', 365),
('5 Years', 'Cancer Risk Reduction', 'Risk of mouth, throat, and bladder cancers cut in half', 'shield', 1825),
('10 Years', 'Lung Cancer Protection', 'Risk of lung cancer drops by 50% compared to smokers', 'trending', 3650);

-- Create milestones table
CREATE TABLE mission_fresh.milestones (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  days_required INTEGER NOT NULL,
  category VARCHAR(100) NOT NULL DEFAULT 'general',
  reward_points INTEGER DEFAULT 0,
  icon_type VARCHAR(50) NOT NULL DEFAULT 'trophy',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert milestone data
INSERT INTO mission_fresh.milestones (name, description, days_required, category, reward_points, icon_type) VALUES
('7-Day Champion', 'Completed your first week smoke-free! This is a major accomplishment.', 7, 'weekly', 100, 'trophy'),
('30-Day Warrior', 'One month smoke-free! Your body is already healing significantly.', 30, 'monthly', 300, 'medal'),
('90-Day Master', 'Three months of freedom! Your lung function has improved dramatically.', 90, 'quarterly', 500, 'crown'),
('180-Day Hero', 'Six months smoke-free! You have broken the physical addiction completely.', 180, 'milestone', 750, 'star'),
('365-Day Legend', 'One full year! You have officially transformed your life and health.', 365, 'annual', 1000, 'diamond'),
('2-Year Veteran', 'Two years of smoke-free living! You are an inspiration to others.', 730, 'major', 1500, 'badge'),
('5-Year Master', 'Five years smoke-free! Your health risks have dropped to near-normal levels.', 1825, 'major', 2500, 'crown');

-- Enable RLS
ALTER TABLE mission_fresh.health_benefits ENABLE ROW LEVEL SECURITY;
ALTER TABLE mission_fresh.milestones ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (public read access for health benefits and milestones)
CREATE POLICY "Health benefits are publicly readable" ON mission_fresh.health_benefits
  FOR SELECT USING (true);

CREATE POLICY "Milestones are publicly readable" ON mission_fresh.milestones
  FOR SELECT USING (true);
