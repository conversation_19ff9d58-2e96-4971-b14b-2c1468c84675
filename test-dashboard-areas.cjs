const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  try {
    console.log('🏠 DASHBOARD AND USER INTERFACE TESTING - localhost:5002');
    
    // Test dashboard areas
    const dashboardAreas = [
      { name: 'User Profile', url: '/profile', requiresAuth: true },
      { name: 'My Journey', url: '/my-journey', requiresAuth: true },
      { name: 'Progress Tracking', url: '/progress', requiresAuth: false },
      { name: 'Community Support', url: '/community', requiresAuth: false }
    ];
    
    console.log('\\n📋 Testing Dashboard Areas:');
    
    for (const area of dashboardAreas) {
      try {
        console.log(`\\n🔍 Testing: ${area.name} (${area.url})`);
        
        await page.goto(`http://localhost:5002${area.url}`, { waitUntil: 'domcontentloaded', timeout: 15000 });
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Check page content
        const pageAnalysis = await page.evaluate(() => {
          const body = document.body;
          const title = document.title;
          const content = body.innerText.toLowerCase();
          
          return {
            title: title,
            hasContent: body.innerText.length > 100,
            hasError: content.includes('error') || content.includes('not found'),
            hasAuthRequired: content.includes('authentication required') || content.includes('sign in'),
            hasLoading: content.includes('loading'),
            hasDashboard: content.includes('dashboard') || content.includes('profile') || content.includes('progress'),
            hasUserData: content.includes('welcome') || content.includes('your'),
            contentLength: body.innerText.length
          };
        });
        
        console.log(`📄 Page Title: ${pageAnalysis.title}`);
        console.log(`📊 Content Length: ${pageAnalysis.contentLength} characters`);
        
        if (pageAnalysis.hasError) {
          console.log(`❌ Page shows error content`);
        } else if (pageAnalysis.hasAuthRequired) {
          console.log(`🔒 Authentication required (expected for ${area.requiresAuth ? 'protected' : 'public'} area)`);
        } else if (pageAnalysis.hasLoading) {
          console.log(`⚠️ Page stuck in loading state`);
        } else if (pageAnalysis.hasDashboard) {
          console.log(`✅ Dashboard content detected`);
          
          if (pageAnalysis.hasUserData) {
            console.log(`✅ User data detected`);
          } else {
            console.log(`⚠️ No user data detected`);
          }
        } else if (pageAnalysis.hasContent) {
          console.log(`✅ Page loaded with content`);
        } else {
          console.log(`⚠️ Page loaded but minimal content`);
        }
        
        // Take screenshot
        await page.screenshot({ path: `dashboard-${area.name.toLowerCase().replace(' ', '-')}.png`, fullPage: false });
        console.log(`📸 Screenshot saved: dashboard-${area.name.toLowerCase().replace(' ', '-')}.png`);
        
      } catch (error) {
        console.log(`❌ Error testing ${area.name}: ${error.message}`);
      }
    }
    
    console.log('\\n🏠 DASHBOARD TESTING COMPLETE');
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  
  await browser.close();
})();
