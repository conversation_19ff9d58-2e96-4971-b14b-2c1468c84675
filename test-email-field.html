<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Field Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-container { max-width: 400px; margin: 0 auto; }
        .email-field { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ccc; border-radius: 5px; }
        .debug-info { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function EmailFieldTest() {
            const [email, setEmail] = useState('');
            const [emailError, setEmailError] = useState('');
            const [debugLog, setDebugLog] = useState([]);

            const addDebugLog = (message) => {
                setDebugLog(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
            };

            useEffect(() => {
                addDebugLog('Component mounted, email state initialized');
            }, []);

            const validateEmail = (email) => {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!email) {
                    setEmailError('Email address is required');
                    return false;
                }
                if (!emailRegex.test(email)) {
                    setEmailError('Please enter a valid email address');
                    return false;
                }
                setEmailError('');
                return true;
            };

            const handleEmailChange = (e) => {
                const value = e.target.value;
                addDebugLog(`Email onChange triggered: "${value}"`);
                setEmail(value);
                
                // Clear error when user starts typing
                if (emailError && value.length > 0) {
                    setEmailError('');
                }
            };

            const handleEmailBlur = () => {
                addDebugLog(`Email onBlur triggered, validating: "${email}"`);
                validateEmail(email);
            };

            const testEmailField = () => {
                addDebugLog('Testing email field functionality...');
                
                // Test 1: Set valid email
                setEmail('<EMAIL>');
                addDebugLog('Set email to: <EMAIL>');
                
                setTimeout(() => {
                    // Test 2: Validate
                    const isValid = validateEmail('<EMAIL>');
                    addDebugLog(`Validation result: ${isValid ? 'VALID' : 'INVALID'}`);
                }, 100);
            };

            return (
                <div className="test-container">
                    <h1>Email Field React State Test</h1>
                    
                    <div>
                        <label htmlFor="email">Email Address:</label>
                        <input
                            id="email"
                            type="email"
                            value={email}
                            onChange={handleEmailChange}
                            onBlur={handleEmailBlur}
                            className="email-field"
                            placeholder="Enter your email address"
                        />
                        {emailError && (
                            <p className="error">{emailError}</p>
                        )}
                    </div>

                    <div className="debug-info">
                        <h3>Current State:</h3>
                        <p><strong>Email Value:</strong> "{email}"</p>
                        <p><strong>Email Error:</strong> "{emailError}"</p>
                        <p><strong>Email Length:</strong> {email.length}</p>
                    </div>

                    <button onClick={testEmailField} style={{padding: '10px 20px', margin: '10px 0'}}>
                        Run Automated Test
                    </button>

                    <div className="debug-info">
                        <h3>Debug Log:</h3>
                        {debugLog.map((log, index) => (
                            <div key={index} style={{fontSize: '12px', marginBottom: '2px'}}>
                                {log}
                            </div>
                        ))}
                    </div>

                    <div className="debug-info">
                        <h3>Test Instructions:</h3>
                        <ol>
                            <li>Type in the email field above</li>
                            <li>Watch the "Current State" section update</li>
                            <li>Check if onChange events are logged</li>
                            <li>Click "Run Automated Test" to test programmatic changes</li>
                            <li>If email state updates correctly, ERROR 8 is RESOLVED</li>
                        </ol>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<EmailFieldTest />, document.getElementById('root'));
    </script>
</body>
</html>
