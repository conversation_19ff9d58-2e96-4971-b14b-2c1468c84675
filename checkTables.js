import { createClient } from '@supabase/supabase-js'

// Get environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://yekarqanirdkdckimpna.supabase.co'
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc'

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  db: {
    schema: 'mission_fresh'
  },
  auth: {
    persistSession: false
  }
})

async function checkTables() {
  console.log('🔍 Checking database tables...')
  
  try {
    // Check vendors table structure
    console.log('\n📋 Checking vendors table...')
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('vendors')
      .select('*')
      .limit(1)
    
    if (error) {
      console.error('❌ Vendors table error:', error)
      
      // Try to create vendors table
      console.log('🔨 Creating vendors table...')
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS mission_fresh.vendors (
          id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
          name text NOT NULL,
          website text NOT NULL,
          logo_url text,
          description text NOT NULL,
          rating numeric(3,2) DEFAULT 0,
          review_count integer DEFAULT 0,
          shipping_info text NOT NULL,
          min_order numeric(10,2) DEFAULT 0,
          delivery_time text NOT NULL,
          coverage_areas text[] DEFAULT '{}',
          specialties text[] DEFAULT '{}',
          verified boolean DEFAULT false,
          affiliate_commission numeric(5,2) DEFAULT 0,
          created_at timestamp with time zone DEFAULT now(),
          updated_at timestamp with time zone DEFAULT now()
        );
      `
      
      const { error: createError } = await supabase.rpc('exec_sql', { sql: createTableQuery })
      if (createError) {
        console.error('❌ Error creating vendors table:', createError)
      } else {
        console.log('✅ Vendors table created successfully')
      }
    } else {
      console.log('✅ Vendors table exists')
    }

  } catch (err) {
    console.error('❌ Error:', err)
  }
}

checkTables()
