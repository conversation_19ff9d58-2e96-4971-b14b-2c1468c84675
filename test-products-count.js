import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://yekarqanirdkdckimpna.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc';

const supabase = createClient(supabaseUrl, supabaseKey, {
  db: { schema: 'mission_fresh' }
});

// Replicate the exact getNRTProducts function from supabase.ts
const getNRTProducts = async () => {
  try {
    console.log('getNRTProducts: Fetching FDA-approved NRT products from mission_fresh.smokeless_products...');

    const { data, error } = await supabase
      .from('smokeless_products')
      .select(`
        id,
        name,
        brand,
        category,
        description,
        image_url,
        user_rating_avg,
        user_rating_count,
        nicotine_strengths,
        flavors,
        created_at
      `)
      .order('name', { ascending: true });

    if (!error && data && data.length > 0) {
      console.log('getNRTProducts: Fetched', data.length, 'products from smokeless_products table');
      return data.map(product => ({
        id: product.id,
        name: product.name,
        brand: product.brand,
        category: product.category,
        description: product.description,
        image_url: product.image_url,
        user_rating_avg: product.user_rating_avg || 0,
        user_rating_count: product.user_rating_count || 0,
        nicotine_strengths: product.nicotine_strengths || [],
        flavors: product.flavors || ['Original'],
        created_at: product.created_at
      }));
    }

    console.log('getNRTProducts: No data found or error:', error);
    return [];
  } catch (error) {
    console.error('getNRTProducts: Error:', error);
    return [];
  }
};

async function testProductsCount() {
  console.log('🧪 Testing products count exactly as used in LandingPage...');
  
  try {
    const products = await getNRTProducts();
    const productsCount = products.length;
    
    console.log(`✅ getNRTProducts returned ${productsCount} products`);
    
    if (productsCount > 0) {
      console.log('📋 Sample products:');
      products.slice(0, 5).forEach((product, index) => {
        console.log(`  ${index + 1}. ${product.name} (${product.brand})`);
      });
      
      console.log(`\n🎯 Landing page will display: "${productsCount}+" products`);
      console.log('✅ ERROR 3 RESOLVED - Products count is truly dynamic from database');
      console.log('✅ HOLY RULE #1 COMPLIANT - No hardcoded product count');
      
      // Test the exact display logic from LandingPage
      const displayText = productsCount > 0 ? `${productsCount}+` : 'available';
      console.log(`📱 Actual display text: "${displayText}"`);
      
    } else {
      console.log('❌ ERROR 3 confirmed - No products found in database');
      console.log('🔧 Products count would show "available" instead of dynamic number');
    }
    
  } catch (error) {
    console.error('❌ Error testing products count:', error);
  }
}

// Run the test
testProductsCount().catch(console.error);
