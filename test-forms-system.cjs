const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  try {
    console.log('📝 FORMS AND DATA INPUT TESTING - localhost:5002');
    
    // Test form-related pages
    const formPages = [
      { name: 'Landing Page (Newsletter)', url: '/' },
      { name: 'Contact Page', url: '/contact' },
      { name: 'Reviews Page (Review Form)', url: '/reviews' },
      { name: 'User Profile', url: '/profile' },
      { name: 'Progress Page', url: '/progress' }
    ];
    
    console.log('\\n📋 Testing Forms System:');
    
    for (const pageInfo of formPages) {
      try {
        console.log(`\\n🔍 Testing: ${pageInfo.name} (${pageInfo.url})`);
        
        await page.goto(`http://localhost:5002${pageInfo.url}`, { waitUntil: 'domcontentloaded', timeout: 15000 });
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Check forms and input elements
        const formsAnalysis = await page.evaluate(() => {
          const forms = document.querySelectorAll('form');
          const inputs = document.querySelectorAll('input');
          const textareas = document.querySelectorAll('textarea');
          const selects = document.querySelectorAll('select');
          const buttons = document.querySelectorAll('button');
          
          // Analyze form types
          const emailInputs = Array.from(inputs).filter(input => 
            input.type === 'email' || input.name?.includes('email') || input.placeholder?.toLowerCase().includes('email')
          );
          const passwordInputs = Array.from(inputs).filter(input => input.type === 'password');
          const submitButtons = Array.from(buttons).filter(btn => 
            btn.type === 'submit' || 
            btn.textContent?.toLowerCase().includes('submit') ||
            btn.textContent?.toLowerCase().includes('send') ||
            btn.textContent?.toLowerCase().includes('save')
          );
          
          // Check for validation attributes
          const requiredInputs = Array.from(inputs).filter(input => input.required);
          const patternInputs = Array.from(inputs).filter(input => input.pattern);
          
          // Check for error handling elements
          const errorElements = document.querySelectorAll('[class*="error"], [class*="invalid"], [role="alert"]');
          
          return {
            formsCount: forms.length,
            inputsCount: inputs.length,
            textareasCount: textareas.length,
            selectsCount: selects.length,
            buttonsCount: buttons.length,
            emailInputsCount: emailInputs.length,
            passwordInputsCount: passwordInputs.length,
            submitButtonsCount: submitButtons.length,
            requiredInputsCount: requiredInputs.length,
            patternInputsCount: patternInputs.length,
            errorElementsCount: errorElements.length,
            submitButtonsText: submitButtons.map(btn => btn.textContent?.trim()).filter(Boolean),
            hasAuthRequired: document.body.innerText.toLowerCase().includes('authentication required'),
            hasError: document.body.innerText.toLowerCase().includes('error'),
            contentLength: document.body.innerText.length
          };
        });
        
        console.log(`📊 Content Length: ${formsAnalysis.contentLength} characters`);
        
        if (formsAnalysis.hasAuthRequired) {
          console.log(`🔒 Authentication required`);
        } else if (formsAnalysis.hasError) {
          console.log(`❌ Page shows error content`);
        } else {
          console.log(`✅ Page loaded successfully`);
        }
        
        // Forms analysis
        console.log(`  📋 Forms: ${formsAnalysis.formsCount}`);
        console.log(`  📝 Inputs: ${formsAnalysis.inputsCount}`);
        console.log(`  📄 Textareas: ${formsAnalysis.textareasCount}`);
        console.log(`  📊 Selects: ${formsAnalysis.selectsCount}`);
        console.log(`  🔘 Buttons: ${formsAnalysis.buttonsCount}`);
        console.log(`  📧 Email Inputs: ${formsAnalysis.emailInputsCount}`);
        console.log(`  🔒 Password Inputs: ${formsAnalysis.passwordInputsCount}`);
        console.log(`  ✅ Submit Buttons: ${formsAnalysis.submitButtonsCount} (${formsAnalysis.submitButtonsText.join(', ')})`);
        console.log(`  ⚠️ Required Inputs: ${formsAnalysis.requiredInputsCount}`);
        console.log(`  🔍 Pattern Validation: ${formsAnalysis.patternInputsCount}`);
        console.log(`  ❌ Error Elements: ${formsAnalysis.errorElementsCount}`);
        
        // Take screenshot
        const filename = `forms-${pageInfo.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}.png`;
        await page.screenshot({ path: filename, fullPage: false });
        console.log(`📸 Screenshot saved: ${filename}`);
        
      } catch (error) {
        console.log(`❌ Error testing ${pageInfo.name}: ${error.message}`);
      }
    }
    
    // Test authentication modal forms
    console.log('\\n🔐 Testing Authentication Modal Forms:');
    
    try {
      await page.goto('http://localhost:5002', { waitUntil: 'domcontentloaded' });
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Click Sign In button to open modal
      await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const signInButton = buttons.find(btn => 
          btn.textContent.toLowerCase().includes('sign in')
        );
        if (signInButton) {
          signInButton.click();
        }
      });
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Test authentication form
      const authFormAnalysis = await page.evaluate(() => {
        const modal = document.querySelector('[role="dialog"], [class*="modal"], [class*="Modal"]');
        if (!modal) return { modalFound: false };
        
        const forms = modal.querySelectorAll('form');
        const inputs = modal.querySelectorAll('input');
        const buttons = modal.querySelectorAll('button');
        
        const emailInputs = Array.from(inputs).filter(input => 
          input.type === 'email' || input.name?.includes('email')
        );
        const passwordInputs = Array.from(inputs).filter(input => input.type === 'password');
        const submitButtons = Array.from(buttons).filter(btn => 
          btn.type === 'submit' || btn.textContent?.toLowerCase().includes('sign in')
        );
        
        return {
          modalFound: true,
          formsCount: forms.length,
          inputsCount: inputs.length,
          emailInputsCount: emailInputs.length,
          passwordInputsCount: passwordInputs.length,
          submitButtonsCount: submitButtons.length,
          hasValidation: Array.from(inputs).some(input => input.required)
        };
      });
      
      if (authFormAnalysis.modalFound) {
        console.log('✅ Authentication modal found');
        console.log(`  📋 Forms: ${authFormAnalysis.formsCount}`);
        console.log(`  📝 Inputs: ${authFormAnalysis.inputsCount}`);
        console.log(`  📧 Email Inputs: ${authFormAnalysis.emailInputsCount}`);
        console.log(`  🔒 Password Inputs: ${authFormAnalysis.passwordInputsCount}`);
        console.log(`  ✅ Submit Buttons: ${authFormAnalysis.submitButtonsCount}`);
        console.log(`  ⚠️ Has Validation: ${authFormAnalysis.hasValidation ? '✅' : '❌'}`);
        
        await page.screenshot({ path: 'forms-auth-modal.png', fullPage: false });
        console.log('📸 Screenshot saved: forms-auth-modal.png');
      } else {
        console.log('❌ Authentication modal not found');
      }
      
    } catch (error) {
      console.log(`❌ Error testing authentication modal: ${error.message}`);
    }
    
    console.log('\\n📝 FORMS AND DATA INPUT TESTING COMPLETE');
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  
  await browser.close();
})();
