{"timestamp": "2025-07-31T22:43:36.800Z", "screenshots": [{"route": "/", "name": "homepage", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/nrt", "name": "nrt-directory", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/products", "name": "products-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/brands", "name": "brands-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/stores", "name": "stores-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/vendors", "name": "vendors-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/store-locator", "name": "store-locator", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/search", "name": "search-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/discover", "name": "discover-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/reviews", "name": "reviews-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/deals", "name": "deals-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/price-compare", "name": "price-compare", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/smokeless", "name": "smokeless-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/retailers", "name": "retailers-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/community", "name": "community-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/progress", "name": "progress-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/profile", "name": "profile-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/about", "name": "about-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/contact", "name": "contact-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/privacy", "name": "privacy-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}, {"route": "/terms", "name": "terms-page", "screenshot": null, "error": "page.waitForTimeout is not a function"}], "summary": {"total": 21, "successful": 0, "failed": 21}}