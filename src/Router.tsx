import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

// Universal Layout Component
import Layout from './components/Layout';
import ErrorBoundary from './components/ErrorBoundary';

// Landing Page - Keep as direct import for fastest initial load
import LandingPage from './pages/LandingPage';

// Lazy load all other pages for better performance
const ProductDetailPage = React.lazy(() => import('./pages/ProductDetailPage'));
const NRTPage = React.lazy(() => import('./pages/NRTPage'));
const CategoryPage = React.lazy(() => import('./pages/CategoryPage'));
const BrandPage = React.lazy(() => import('./pages/BrandPage'));
const BrandsPage = React.lazy(() => import('./pages/BrandsPage'));
const StoreDetailPage = React.lazy(() => import('./pages/StoreDetailPage'));
const StoresPage = React.lazy(() => import('./pages/StoresPage'));
const VendorDetailPage = React.lazy(() => import('./pages/VendorDetailPage'));
const SearchResultsPage = React.lazy(() => import('./pages/SearchResultsPage'));
const ReviewsPage = React.lazy(() => import('./pages/ReviewsPage'));
const DealsPage = React.lazy(() => import('./pages/DealsPage'));
const VendorsPage = React.lazy(() => import('./pages/VendorsPage'));
const DiscoverPage = React.lazy(() => import('./pages/DiscoverPage'));
const StoreLocator = React.lazy(() => import('./components/StoreLocator'));
const UserProfilePage = React.lazy(() => import('./pages/UserProfilePage'));
const RetailersPage = React.lazy(() => import('./pages/RetailersPage'));
const CommunityPage = React.lazy(() => import('./pages/CommunityPage'));
const ProgressPage = React.lazy(() => import('./pages/ProgressPage'));
const SmokelessPage = React.lazy(() => import('./pages/SmokelessPage'));
const PriceComparePage = React.lazy(() => import('./pages/PriceComparePage'));
const PrivacyPage = React.lazy(() => import('./pages/PrivacyPage'));
const TermsPage = React.lazy(() => import('./pages/TermsPage'));
const AboutPage = React.lazy(() => import('./pages/AboutPage'));
const ContactPage = React.lazy(() => import('./pages/ContactPage'));

import { AuthProvider } from './contexts/AuthContext';

const AppRouter: React.FC = () => {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <Router>
        <Suspense fallback={<div className="min-h-screen bg-background flex items-center justify-center"><div className="text-primary">Loading...</div></div>}>
          <Routes>
            {/* Landing Page - Has its own header/footer */}
            <Route path="/" element={<LandingPage />} />

            {/* NRT DIRECTORY - MAIN DIRECTORY ROUTES */}
            <Route path="/nrt" element={<Layout><NRTPage /></Layout>} />
            <Route path="/nrt-directory" element={<Layout><NRTPage /></Layout>} />
            <Route path="/directory" element={<Layout><NRTPage /></Layout>} />
            <Route path="/products" element={<Layout><NRTPage /></Layout>} />
            <Route path="/product/:id" element={<Layout><ProductDetailPage /></Layout>} />
            <Route path="/product/:id/:slug" element={<Layout><ProductDetailPage /></Layout>} />

            {/* SEO-Optimized Category Pages */}
            <Route path="/category/:category" element={<Layout><CategoryPage /></Layout>} />
            <Route path="/category/:category/:subcategory" element={<Layout><CategoryPage /></Layout>} />

            {/* SEO-Optimized Brand Pages */}
            <Route path="/brands" element={<Layout><BrandsPage /></Layout>} />
            <Route path="/brand/:brand" element={<Layout><BrandPage /></Layout>} />
            <Route path="/brand/:brand/products" element={<Layout><BrandPage /></Layout>} />

            {/* SEO-Optimized Store Pages */}
            <Route path="/stores" element={<Layout><StoresPage /></Layout>} />
            <Route path="/store/:id" element={<Layout><StoreDetailPage /></Layout>} />
            <Route path="/store/:id/:slug" element={<Layout><StoreDetailPage /></Layout>} />
            <Route path="/store-locator" element={<Layout><StoreLocator /></Layout>} />

          {/* SEO-Optimized Vendor Pages */}
          <Route path="/vendor/:id" element={<Layout><VendorDetailPage /></Layout>} />
          <Route path="/vendor/:id/:slug" element={<Layout><VendorDetailPage /></Layout>} />
          <Route path="/vendors" element={<Layout><VendorsPage /></Layout>} />
          <Route path="/online-vendors" element={<Layout><VendorsPage /></Layout>} />

          {/* SEO-Optimized Search & Discovery */}
          <Route path="/search" element={<Layout><SearchResultsPage /></Layout>} />
          <Route path="/discover" element={<Layout><DiscoverPage /></Layout>} />

          {/* SEO-Optimized Content Pages */}
          <Route path="/reviews" element={<Layout><ReviewsPage /></Layout>} />
          <Route path="/deals" element={<Layout><DealsPage /></Layout>} />
          <Route path="/price-compare" element={<Layout><PriceComparePage /></Layout>} />
          <Route path="/compare" element={<Layout><PriceComparePage /></Layout>} />
          <Route path="/smokeless-alternatives" element={<Layout><SmokelessPage /></Layout>} />
          <Route path="/smokeless" element={<Layout><SmokelessPage /></Layout>} />

          {/* Main Navigation Pages */}
          <Route path="/retailers" element={<Layout><RetailersPage /></Layout>} />
          <Route path="/community" element={<Layout><CommunityPage /></Layout>} />
          <Route path="/progress" element={<Layout><ProgressPage /></Layout>} />

          {/* User Pages */}
          <Route path="/profile" element={<Layout><UserProfilePage /></Layout>} />
          <Route path="/my-journey" element={<Layout><UserProfilePage /></Layout>} />

          {/* Legal & Company Pages */}
          <Route path="/privacy" element={<PrivacyPage />} />
          <Route path="/terms" element={<TermsPage />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/contact" element={<ContactPage />} />

          {/* Fallback */}
          <Route path="*" element={<LandingPage />} />
          </Routes>
        </Suspense>
        </Router>
      </AuthProvider>
    </ErrorBoundary>
  );
};

export default AppRouter;
