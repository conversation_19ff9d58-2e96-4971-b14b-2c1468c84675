import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, Package } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import AuthModal from './AuthModal';

const Header: React.FC = () => {
  const { isAuthenticated, user, signOut } = useAuth();
  const location = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin');

  // Apple Mac Desktop active navigation state helper
  const isActiveRoute = (path: string) => location.pathname === path || location.pathname.startsWith(path + '/');
  const getNavLinkClass = (path: string) =>
    isActiveRoute(path)
      ? "text-primary font-sophisticated transition-all text-sm tracking-wide relative"
      : "text-muted-foreground hover:text-foreground transition-all font-sophisticated text-sm tracking-wide";

  return (
    <header className="sticky top-0 z-50 bg-card/95 backdrop-blur-xl border-b border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Apple Mac Desktop Logo */}
          <Link to="/" className="flex items-center gap-4">
            <div className="w-10 h-10 bg-primary rounded-xl flex items-center justify-center">
              <Package className="w-6 h-6 text-primary-foreground" strokeWidth={2} />
            </div>
            <span className="text-xl font-royal text-foreground">NRTList</span>
          </Link>

          {/* Apple Mac Desktop Navigation - Comprehensive NRT Directory */}
          <nav className="hidden md:flex items-center space-x-12">
            <Link to="/nrt" className={getNavLinkClass('/nrt')} title="Browse and compare all NRT products with detailed specifications">
              NRT Directory
            </Link>
            <Link to="/retailers" className={getNavLinkClass('/retailers')} title="Find verified NRT retailers and store locations nationwide">
              Retailers & Stores
            </Link>
            <Link to="/community" className={getNavLinkClass('/community')} title="Connect with community members and access expert support">
              Community Support
            </Link>
            <Link to="/progress" className={getNavLinkClass('/progress')} title="Track your quit journey progress with comprehensive analytics">
              Progress Tracking
            </Link>
          </nav>

          {/* Apple Mac Desktop Auth Buttons */}
          <div className="hidden md:flex items-center gap-4">
            {isAuthenticated ? (
              <>
                <span className="text-foreground font-medium">
                  Welcome, {user?.email?.split('@')[0]}
                </span>
                <button
                  onClick={signOut}
                  className="text-muted-foreground hover:text-foreground font-medium transition-colors"
                >
                  Sign Out
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => {
                    setAuthMode('signin');
                    setShowAuthModal(true);
                  }}
                  className="text-muted-foreground hover:text-foreground font-medium transition-colors"
                >
                  Sign In
                </button>
                <button
                  onClick={() => {
                    setAuthMode('signup');
                    setShowAuthModal(true);
                  }}
                  className="bg-primary text-white px-6 py-2 rounded-xl hover:bg-primary/90 font-medium transition-all"
                >
                  Sign Up
                </button>
              </>
            )}
          </div>

          {/* Apple Mac Desktop Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden text-foreground"
            aria-label={isMenuOpen ? "Close navigation menu" : "Open navigation menu"}
            aria-expanded={isMenuOpen}
            aria-controls="mobile-navigation"
          >
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>
      </div>

      {/* Apple Mac Desktop Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-card border-t border-border" id="mobile-navigation">
          <div className="px-4 py-4 space-y-4">
            <Link to="/nrt" className={`block transition-colors ${isActiveRoute('/nrt') ? 'text-primary font-semibold' : 'text-muted-foreground hover:text-foreground font-medium'}`}>NRT Directory</Link>
            <Link to="/retailers" className={`block transition-colors ${isActiveRoute('/retailers') ? 'text-primary font-semibold' : 'text-muted-foreground hover:text-foreground font-medium'}`}>Retailers & Stores</Link>
            <Link to="/community" className={`block transition-colors ${isActiveRoute('/community') ? 'text-primary font-semibold' : 'text-muted-foreground hover:text-foreground font-medium'}`}>Community Support</Link>
            <Link to="/progress" className={`block transition-colors ${isActiveRoute('/progress') ? 'text-primary font-semibold' : 'text-muted-foreground hover:text-foreground font-medium'}`}>Progress Tracking</Link>
            <div className="pt-4 border-t border-border space-y-3">
              {isAuthenticated ? (
                <>
                  <div className="text-foreground font-medium">
                    Welcome, {user?.email?.split('@')[0]}
                  </div>
                  <button
                    onClick={signOut}
                    className="block w-full text-left text-muted-foreground font-medium"
                  >
                    Sign Out
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={() => {
                      setAuthMode('signin');
                      setShowAuthModal(true);
                      setIsMenuOpen(false);
                    }}
                    className="block w-full text-left text-muted-foreground font-medium"
                  >
                    Sign In
                  </button>
                  <button
                    onClick={() => {
                      setAuthMode('signup');
                      setShowAuthModal(true);
                      setIsMenuOpen(false);
                    }}
                    className="block w-full text-left bg-primary text-white px-4 py-2 rounded-xl font-medium"
                  >
                    Sign Up
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Auth Modal */}
      {showAuthModal && (
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          mode={authMode}
        />
      )}
    </header>
  );
};

export default Header;
