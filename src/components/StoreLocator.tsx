import React, { useState, useEffect, useCallback } from 'react';
import { 
  MapPin, Search, Filter, Star, Phone, Globe, Clock, CheckCircle, 
  Package, Truck, Shield, Award, Users,
  Navigation, Store as StoreIcon, Heart, AlertCircle, X
} from 'lucide-react';
import { getStoresWithInventoryAndPricing, supabase } from '../lib/supabase';

interface SophisticatedStore {
  id: string;
  name: string;
  brand?: string;
  chain?: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  country?: string;
  latitude?: number;
  longitude?: number;
  phone?: string;
  website?: string;
  store_hours?: any;
  nrt_brands_carried: string[];
  pharmacy_available: boolean;
  prescription_required: boolean;
  drive_through: boolean;
  parking_available?: boolean;
  wheelchair_accessible?: boolean;
  rating: number;
  review_count: number;
  verified: boolean;
  distance?: number | null;
  store_inventory?: StoreInventory[];
  store_prices?: StorePrice[];
  store_reviews?: StoreReview[];
  store_photos?: string[];
  created_at: string;
  updated_at?: string;
}

interface StoreInventory {
  id: string;
  product_id: string;
  product_name: string;
  brand: string;
  category: string;
  flavors_available: string[];
  nicotine_strengths_available: string[];
  in_stock: boolean;
  stock_level: 'high' | 'medium' | 'low' | 'out_of_stock';
  last_updated: string;
}

interface StorePrice {
  id: string;
  product_id: string;
  product_name: string;
  brand: string;
  category: string;
  flavor?: string;
  nicotine_strength?: string;
  price_regular: number;
  price_sale?: number;
  price_bulk?: number;
  bulk_quantity?: number;
  currency: string;
  price_per_unit: number;
  unit_type: string;
  discount_percentage?: number;
  sale_end_date?: string;
  last_updated: string;
}

interface StoreReview {
  id: string;
  rating: number;
  review_text?: string;
  helpful_count: number;
  verified_purchase: boolean;
  review_categories?: any;
  created_at: string;
}

interface SophisticatedFilters {
  searchQuery: string;
  productId: string;
  category: string;
  brand: string;
  flavor: string;
  nicotineStrength: string;
  maxDistance: number;
  sortBy: 'distance' | 'price' | 'rating' | 'availability';
  priceRange: { min: number; max: number };
  storeFeatures: {
    pharmacy: boolean;
    driveThrough: boolean;
    parking: boolean;
    wheelchair: boolean;
  };
}

interface StoreLocatorProps {
  productId?: string;
  category?: string;
}

const StoreLocator: React.FC<StoreLocatorProps> = ({ productId = 'all', category = 'all' }) => {
  const [stores, setStores] = useState<SophisticatedStore[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [location, setLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [locationPermission, setLocationPermission] = useState<'pending' | 'granted' | 'denied'>('pending');
  const [showFilters, setShowFilters] = useState(false);

  
  // Sophisticated filters state
  const [filters, setFilters] = useState<SophisticatedFilters>({
    searchQuery: '',
    productId: productId || '',
    category: category || '',
    brand: '',
    flavor: '',
    nicotineStrength: '',
    maxDistance: 25,
    sortBy: 'distance',
    priceRange: { min: 0, max: 200 },
    storeFeatures: {
      pharmacy: false,
      driveThrough: false,
      parking: false,
      wheelchair: false
    }
  });

  // Request geolocation permission with timeout fallback
  const requestLocation = () => {
    if (navigator.geolocation) {
      setLocationPermission('pending');
      
      // CRITICAL FIX: Add timeout to force fallback after 5 seconds
      const timeoutId = setTimeout(() => {
        console.log('🚨 GEOLOCATION TIMEOUT: Forcing fallback to load stores without location');
        setLocationPermission('denied');
      }, 5000);
      
      navigator.geolocation.getCurrentPosition(
        (position) => {
          clearTimeout(timeoutId);
          setLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
          setLocationPermission('granted');
        },
        (error) => {
          clearTimeout(timeoutId);
          console.error('Error getting location:', error);
          setLocationPermission('denied');
        },
        { timeout: 5000 } // Additional timeout option for getCurrentPosition
      );
    } else {
      setLocationPermission('denied');
    }
  };

  // BULLETPROOF STORE LOADING: Always fetch stores on component mount, regardless of geolocation
  useEffect(() => {
    console.log('🚨 STORE LOCATOR: Component mounted, initializing...');
    
    try {
      console.log('🚨 STORE LOCATOR: Calling real fetchSophisticatedStores...');
      fetchSophisticatedStores().catch(err => {
        console.error('🚨 STORE LOCATOR: fetchSophisticatedStores failed:', err);
        setError('Failed to load stores. Please refresh the page.');
        setLoading(false);
      });
    } catch (err) {
      console.error('🚨 STORE LOCATOR: Error in test function:', err);
      setError('Failed to load stores. Please refresh the page.');
      setLoading(false);
    }
    
    try {
      console.log('🚨 STORE LOCATOR: Requesting location...');
      requestLocation();
    } catch (err) {
      console.error('🚨 STORE LOCATOR: Error in requestLocation:', err);
    }
    
    // Fallback: Force loading to false after 10 seconds to prevent infinite loading
    const fallbackTimeout = setTimeout(() => {
      console.log('🚨 STORE LOCATOR: Fallback timeout triggered - stopping loading state');
      setLoading(false);
    }, 10000);
    
    return () => clearTimeout(fallbackTimeout);
  }, []);

  // Fetch sophisticated stores when dependencies change
  useEffect(() => {
    console.log('🚨 STORE LOCATOR: Dependencies changed, refetching stores');
    fetchSophisticatedStores();
  }, [location, productId, category]);

  // Additional fetch when geolocation is denied (backup)
  useEffect(() => {
    console.log('🚨 STORE LOCATOR: Permission status changed:', locationPermission);
    if (locationPermission === 'denied') {
      console.log('🚨 STORE LOCATOR: Location denied, fetching without location');
      fetchSophisticatedStores();
    }
  }, [locationPermission]);

  const fetchSophisticatedStores = useCallback(async () => {
    try {
      console.log('🚨 StoreLocator: Starting fetchSophisticatedStores...');
      setLoading(true);
      setError(null);

      // HOLY RULE #1 COMPLIANT: Only use REAL database data - NO SEEDING FAKE DATA

      const sophisticatedFilters = {
        productId: filters.productId || productId,
        category: filters.category || category,
        brand: filters.brand,
        flavor: filters.flavor,
        nicotineStrength: filters.nicotineStrength,
        maxDistance: filters.maxDistance,
        userLat: location?.lat,
        userLng: location?.lng,
        sortBy: filters.sortBy,
        priceRange: filters.priceRange
      };

      console.log('🚨 StoreLocator: Calling getStoresWithInventoryAndPricing with filters:', sophisticatedFilters);
      
      console.log('🚨 StoreLocator: Calling simplified getStoresWithInventoryAndPricing...');
      const sophisticatedStores = await getStoresWithInventoryAndPricing(sophisticatedFilters);
      console.log('🚨 StoreLocator: Successfully got stores:', sophisticatedStores?.length || 0);
      


      
      // Apply additional feature filters
      let filteredStores = sophisticatedStores;
      
      if (filters.storeFeatures.pharmacy) {
        filteredStores = filteredStores.filter(store => store.pharmacy_available);
      }
      
      if (filters.storeFeatures.driveThrough) {
        filteredStores = filteredStores.filter(store => store.drive_through);
      }
      
      if (filters.storeFeatures.parking) {
        filteredStores = filteredStores.filter(store => store.parking_available);
      }
      
      if (filters.storeFeatures.wheelchair) {
        filteredStores = filteredStores.filter(store => store.wheelchair_accessible);
      }
      
      // Apply search query filter
      if (filters.searchQuery.trim()) {
        const query = filters.searchQuery.toLowerCase();
        filteredStores = filteredStores.filter(store => 
          store.name.toLowerCase().includes(query) ||
          store.address.toLowerCase().includes(query) ||
          store.city.toLowerCase().includes(query) ||
          store.nrt_brands_carried.some((brand: string) => brand.toLowerCase().includes(query))
        );
      }
      
      setStores(filteredStores);
      setError(null);

    } catch (err) {
      console.error('🚨 StoreLocator: Error in fetchSophisticatedStores:', err);
      try {
        setError('Failed to load stores. Please try again.');
        setStores([]);
      } catch (stateError) {
        console.error('🚨 StoreLocator: Error setting error state:', stateError);
      }
    } finally {
      try {
        setLoading(false);
      } catch (loadingError) {
        console.error('🚨 StoreLocator: Error setting loading state:', loadingError);
      }
    }
  }, [location, productId, category]);

  const updateFilter = (key: keyof SophisticatedFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const updateStoreFeature = (feature: keyof SophisticatedFilters['storeFeatures'], value: boolean) => {
    setFilters(prev => ({
      ...prev,
      storeFeatures: { ...prev.storeFeatures, [feature]: value }
    }));
  };

  // Helper functions for store hours
  const getCurrentDayStatus = (hours: any): string => {
    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const todayHours = hours[today];
    if (!todayHours) return 'Hours not available';
    if (todayHours === 'Closed') return 'Closed today';
    return `Today: ${todayHours}`;
  };

  const isStoreOpen = (hours: any): boolean => {
    const now = new Date();
    const today = now.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const todayHours = hours[today];

    if (!todayHours || todayHours === 'Closed') return false;

    // Simple check - in production would parse actual hours
    const currentHour = now.getHours();
    return currentHour >= 8 && currentHour < 22; // Assume most stores open 8am-10pm
  };

  // Helper function for inventory status colors
  const getStockLevelColor = (stockLevel: string): string => {
    switch (stockLevel.toLowerCase()) {
      case 'in_stock':
      case 'high':
        return 'bg-background border border-wellness text-wellness';
      case 'low_stock':
      case 'low':
        return 'bg-background border border-rating-gold text-rating-gold';
      case 'out_of_stock':
      case 'out':
        return 'bg-background border border-alert-red text-alert-red';
      case 'limited':
        return 'bg-background border border-rating-gold text-rating-gold';
      default:
        return 'bg-background border border-wellness text-wellness';
    }
  };

  const getLowestPrice = (store: SophisticatedStore) => {
    if (!store.store_prices || store.store_prices.length === 0) return null;
    return Math.min(...store.store_prices.map(p => p.price_regular));
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Enhanced Header Section with Reasonable Font Sizes */}
      <div className="bg-background/98 backdrop-blur-sm border-b border-border/30 shadow-[0_1px_3px_rgba(0,0,0,0.04)]">
        <div className="max-w-7xl mx-auto px-8 py-10">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-royal text-foreground mb-4 tracking-royal">Find NRT Stores Near You</h1>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto font-sophisticated leading-relaxed">
              Locate pharmacies, supermarkets, and stores that sell NRT products in your area.
              Get real-time inventory and pricing information.
            </p>
            <p className="text-sm text-muted-foreground mt-3 font-sophisticated">
              When cravings hit, find help nearby.
            </p>
          </div>
        </div>
      </div>

      <main className="py-6">
        <div className="max-w-7xl mx-auto px-6 py-6">
          {/* Enhanced Search Header with Reasonable Font Sizes */}
          <div className="bg-background/98 backdrop-blur-md rounded-2xl border border-border/30 p-6 mb-6 shadow-[0_2px_8px_rgba(0,0,0,0.04)]">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Main Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search stores, products, or locations..."
                    value={filters.searchQuery}
                    onChange={(e) => updateFilter('searchQuery', e.target.value)}
                    className="w-full pl-12 pr-4 py-3 border border-border/30 rounded-xl focus:ring-2 focus:ring-primary/30 focus:border-primary/40 text-base font-sophisticated transition-all duration-300 shadow-[0_1px_3px_rgba(0,0,0,0.04)]"
                  />
            </div>
          </div>

          {/* Location Permission Button */}
          {locationPermission === 'denied' && (
            <button
              onClick={requestLocation}
              className="flex items-center gap-2 px-3 py-2 bg-primary text-background rounded-md hover:bg-primary/90 transition-colors text-sm"
            >
              <Navigation className="w-4 h-4" />
              Enable Location
            </button>
          )}

          {locationPermission === 'pending' && (
            <div className="flex items-center gap-2 px-3 py-2 bg-background text-muted-foreground rounded-md border border-muted text-sm">
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary"></div>
              Getting Location...
            </div>
          )}

          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2 px-3 py-2 bg-muted text-foreground rounded-md hover:bg-muted/80 transition-colors text-sm"
          >
            <Filter className="w-4 h-4" />
            Advanced Filters
          </button>
          </div>

        {/* Advanced Filters Panel */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-muted">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Product Category */}
              <div>
                <label className="block text-sm font-medium text-wellness mb-2">Category</label>
                <select
                  value={filters.category}
                  onChange={(e) => updateFilter('category', e.target.value)}
                  className="w-full px-3 py-2 border border-wellness rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                >
                  <option value="">All Categories</option>
                  <option value="gum">Nicotine Gum</option>
                  <option value="lozenges">Lozenges</option>
                  <option value="patches">Patches</option>
                  <option value="pouches">Pouches</option>
                </select>
              </div>

              {/* Brand */}
              <div>
                <label className="block text-sm font-medium text-wellness mb-2">Brand</label>
                <select
                  value={filters.brand}
                  onChange={(e) => updateFilter('brand', e.target.value)}
                  className="w-full px-3 py-2 border border-wellness rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                >
                  <option value="">All Brands</option>
                  <option value="Nicorette">Nicorette</option>
                  <option value="NicoDerm CQ">NicoDerm CQ</option>
                  <option value="Commit">Commit</option>
                  <option value="ZYN">ZYN</option>
                </select>
              </div>

              {/* Distance */}
              <div>
                <label className="block text-sm font-medium text-wellness mb-2">Max Distance</label>
                <select
                  value={filters.maxDistance}
                  onChange={(e) => updateFilter('maxDistance', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-wellness rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                >
                  <option value={5}>5 miles</option>
                  <option value={10}>10 miles</option>
                  <option value={25}>25 miles</option>
                  <option value={50}>50 miles</option>
                  <option value={100}>100 miles</option>
                </select>
              </div>

              {/* Sort By with Visual Indicators */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Sort By
                  <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                    {filters.sortBy === 'distance' ? '📍 Distance' :
                     filters.sortBy === 'price' ? '💰 Price' :
                     filters.sortBy === 'rating' ? '⭐ Rating' : '📦 Availability'}
                  </span>
                </label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => updateFilter('sortBy', e.target.value)}
                  className="w-full px-3 py-2 border border-wellness rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                >
                  <option value="distance">📍 Distance (Nearest First)</option>
                  <option value="price">💰 Lowest Price</option>
                  <option value="rating">⭐ Highest Rated</option>
                  <option value="availability">📦 Best Availability</option>
                </select>
              </div>
            </div>

            {/* Enhanced Store Features & Amenities */}
            <div className="mt-4">
              <label className="block text-sm font-medium text-wellness mb-3">Store Features & Amenities</label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {[
                  { key: 'pharmacy', label: 'Pharmacy', icon: Shield, color: 'text-wellness' },
                  { key: 'driveThrough', label: 'Drive-Through', icon: Truck, color: 'text-wellness' },
                  { key: 'parking', label: 'Free Parking', icon: Package, color: 'text-wellness' },
                  { key: 'wheelchair', label: 'Wheelchair Access', icon: Heart, color: 'text-wellness' },
                  { key: 'consultation', label: 'Pharmacist Consultation', icon: Users, color: 'text-wellness' },
                  { key: 'delivery', label: 'Home Delivery', icon: Truck, color: 'text-wellness' },
                  { key: 'rewards', label: 'Rewards Program', icon: Award, color: 'text-rating-gold' },
                  { key: 'insurance', label: 'Insurance Accepted', icon: Shield, color: 'text-alert-red' }
                ].map(({ key, label, icon: Icon, color }) => (
                  <label key={key} className="flex items-center gap-2 cursor-pointer p-2 rounded-lg hover:bg-muted transition-colors">
                    <input
                      type="checkbox"
                      checked={filters.storeFeatures[key as keyof typeof filters.storeFeatures] || false}
                      onChange={(e) => updateStoreFeature(key as keyof typeof filters.storeFeatures, e.target.checked)}
                      className="rounded border-wellness text-wellness focus:ring-wellness"
                    />
                    <Icon className={`w-4 h-4 ${color}`} />
                    <span className="text-sm text-wellness font-medium">{label}</span>
                  </label>
                ))}
              </div>

              {/* Active Filters Display */}
              <div className="mt-3 flex flex-wrap gap-2">
                {Object.entries(filters.storeFeatures).filter(([_, value]) => value).map(([key, _]) => (
                  <span key={key} className="inline-flex items-center gap-1 bg-wellness/10 text-wellness px-2 py-1 rounded-full text-xs font-medium">
                    {key.charAt(0).toUpperCase() + key.slice(1)}
                    <button
                      onClick={() => updateStoreFeature(key as keyof typeof filters.storeFeatures, false)}
                      className="hover:bg-wellness/20 rounded-full p-0.5"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

        {/* Enhanced Results Summary with Reasonable Font Sizes */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-royal text-foreground tracking-royal">
            {loading ? 'Searching...' : `${stores.length} stores found`}
          </h2>
          {location ? (
            <div className="flex items-center gap-2 text-sm text-muted-foreground font-sophisticated">
              <Navigation className="w-4 h-4" />
              Near your location
            </div>
          ) : null}
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-3 text-muted-foreground text-sm">Finding the best stores for you...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <p className="text-red-800">{error}</p>
          </div>
        </div>
      )}



      {/* Store Results */}
      {!loading && !error && stores.length === 0 && (
        <div className="text-center py-12">
          <StoreIcon className="w-16 h-16 text-wellness mx-auto mb-4" />
          <h3 className="text-lg font-medium text-wellness mb-2">No stores found</h3>
          <p className="text-wellness">Try adjusting your filters or search criteria.</p>
        </div>
      )}

      {/* Enhanced Store Cards with Modern Styling */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {stores.map((store, index) => (
          <div
            key={store.id}
            className="bg-background/98 backdrop-blur-md rounded-2xl border border-border/30 overflow-hidden hover:border-primary/30 hover:shadow-[0_8px_24px_rgba(0,0,0,0.08)] transition-all duration-500 cursor-pointer group hover:scale-[1.02] animate-fade-in-up"
            style={{animationDelay: `${index * 100}ms`}}
          >
            {/* Store Photo Header */}
            <div className="relative h-32 bg-muted/20">
              {store.store_photos && store.store_photos.length > 0 ? (
                <img
                  src={store.store_photos[0]}
                  alt={`${store.name} storefront`}
                  className="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-500"
                />
              ) : (
                <div className="w-full h-32 bg-background/50 flex items-center justify-center border border-border/20">
                  <StoreIcon className="w-8 h-8 text-muted-foreground" />
                </div>
              )}
              {/* Photo Count Badge */}
              {store.store_photos && store.store_photos.length > 1 && (
                <div className="absolute top-1 right-1 bg-primary text-background text-xs px-1 py-0.5 rounded-md">
                  +{store.store_photos.length - 1} photos
                </div>
              )}
              {/* Store Chain Badge */}
              {store.chain && (
                <div className="absolute top-1 left-1 bg-accent text-background text-xs px-1 py-0.5 rounded-md font-normal">
                  {store.chain}
                </div>
              )}
              {/* Store Open/Closed Status Badge */}
              <div className={`absolute bottom-1 right-1 text-background text-xs px-1 py-0.5 rounded-md font-normal ${
                store.store_hours && isStoreOpen(store.store_hours)
                  ? 'bg-primary'
                  : 'bg-destructive'
              }`}>
                {store.store_hours && isStoreOpen(store.store_hours) ? 'OPEN' : 'CLOSED'}
              </div>
            </div>

            <div className="p-3">
            {/* Store Header with Logo */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-start gap-2 flex-1">
                {/* Store Chain/Brand Logo */}
                <div className="w-8 h-8 bg-background border border-muted rounded-md flex items-center justify-center flex-shrink-0">
                  {store.chain ? (
                    <div className="w-7 h-7 bg-primary rounded-md flex items-center justify-center">
                      <span className="text-background font-normal text-xs">
                        {store.chain.substring(0, 2).toUpperCase()}
                      </span>
                    </div>
                  ) : (
                    <StoreIcon className="w-4 h-4 text-muted-foreground" />
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-1 mb-1">
                    <h3 className="text-sm font-refined text-foreground tracking-refined">{store.name}</h3>
                    {store.verified && (
                      <CheckCircle className="w-3 h-3 text-primary" />
                    )}
                    {store.chain && (
                      <span className="bg-muted text-foreground text-xs px-1 py-0.5 rounded-md font-normal">
                        {store.chain}
                      </span>
                    )}
                  </div>
                  <p className="text-muted-foreground text-xs">{store.address}, {store.city}, {store.state}</p>
                  {store.distance && (
                    <p className="text-xs text-muted-foreground font-normal">{store.distance.toFixed(1)} miles away</p>
                  )}
                </div>
              </div>

              {/* Rating & Comparison */}
              <div className="text-right">
                <div className="flex items-center gap-1 mb-1">
                  <Star className="w-3 h-3 text-primary fill-current" />
                  <span className="font-normal text-xs">{store.rating.toFixed(1)}</span>
                </div>
                <div className="flex items-center gap-1 text-xs text-muted-foreground mb-1">
                  <span>{store.review_count} reviews</span>
                  {store.review_count > 50 && (
                    <span className="bg-background border border-muted text-foreground px-1 py-0.5 rounded text-xs">Popular</span>
                  )}
                  {store.review_count > 100 && (
                    <span className="bg-background border border-muted text-foreground px-1 py-0.5 rounded text-xs">Trusted</span>
                  )}
                </div>

                {/* Store Comparison Checkbox */}
                <label className="flex items-center gap-1 cursor-pointer justify-end">
                  <span className="text-xs text-muted-foreground">Compare</span>
                  <input
                    type="checkbox"
                    className="rounded border-muted text-primary focus:ring-primary"
                    onChange={(e) => {
                      console.log('Store comparison toggled:', store.name, e.target.checked);
                      // In production, would add to comparison state
                    }}
                  />
                </label>
              </div>
            </div>
            </div>

            {/* Store Hours - Enhanced Display */}
            <div className="mb-2">
              <div className="flex items-center gap-2 text-xs">
                <Clock className="w-3 h-3 text-muted-foreground" />
                <div className="flex-1">
                  {store.store_hours ? (
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-normal text-foreground">
                          {getCurrentDayStatus(store.store_hours)}
                        </span>
                        <span className={`text-xs px-1 py-0.5 rounded-md ${isStoreOpen(store.store_hours) ? 'bg-primary/10 text-primary' : 'bg-destructive/10 text-destructive'}`}>
                          {isStoreOpen(store.store_hours) ? 'Open' : 'Closed'}
                        </span>
                      </div>
                      <button
                        className="text-xs text-accent hover:text-accent/80 mt-1"
                        onClick={() => {/* Show full hours modal */}}
                      >
                        View all hours
                      </button>
                    </div>
                  ) : (
                    <span className="text-muted-foreground">Hours not available</span>
                  )}
                </div>
              </div>
            </div>

            {/* Store Features */}
            <div className="flex flex-wrap gap-1 mb-3">
              {store.pharmacy_available && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-muted/30 text-foreground text-xs rounded-md">
                  <Shield className="w-3 h-3" />
                  Pharmacy
                </span>
              )}
              {store.drive_through && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-muted/30 text-foreground text-xs rounded-md">
                  <Truck className="w-3 h-3" />
                  Drive-Through
                </span>
              )}
              {store.parking_available && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-muted/30 text-foreground text-xs rounded-md">
                  <Package className="w-3 h-3" />
                  Parking
                </span>
              )}
              {store.wheelchair_accessible && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-muted/30 text-foreground text-xs rounded-md">
                  <Heart className="w-3 h-3" />
                  Accessible
                </span>
              )}
            </div>

            {/* Enhanced Product Availability with Status Indicators */}
            {store.store_inventory && store.store_inventory.length > 0 && (
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-wellness">Available Products</h4>
                  <span className="text-xs text-wellness">
                    {store.store_inventory.filter(item => item.stock_level !== 'out_of_stock').length} in stock
                  </span>
                </div>
                <div className="space-y-2">
                  {store.store_inventory.slice(0, 3).map((item) => (
                    <div key={item.id} className="flex items-center justify-between p-2 bg-white border border-wellness rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <p className="text-sm font-medium text-wellness">{item.product_name}</p>
                          <div className={`w-2 h-2 rounded-full ${
                            item.stock_level === 'high' ? 'bg-wellness' :
                            item.stock_level === 'medium' ? 'bg-wellness' :
                            item.stock_level === 'low' ? 'bg-rating-gold' :
                            item.stock_level === 'out_of_stock' ? 'bg-alert-red' : 'bg-wellness'
                          }`}></div>
                        </div>
                        <p className="text-xs text-wellness">
                          {item.flavors_available.join(', ')} • {item.nicotine_strengths_available.join(', ')}
                        </p>
                      </div>
                      <div className="text-right">
                        <span className={`px-2 py-1 text-xs rounded-full font-medium ${getStockLevelColor(item.stock_level)}`}>
                          {item.stock_level.replace('_', ' ').toUpperCase()}
                        </span>
                        {item.last_updated && (
                          <p className="text-xs text-muted-foreground mt-1">
                            Updated {new Date(item.last_updated).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                  {store.store_inventory.length > 3 && (
                    <button className="text-xs text-primary hover:text-primary font-medium">
                      View all {store.store_inventory.length} products →
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Pricing */}
            {store.store_prices && store.store_prices.length > 0 && (
              <div className="mb-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-foreground">Starting from</span>
                  <span className="text-lg font-bold text-wellness">${getLowestPrice(store)?.toFixed(2)}</span>
                </div>
              </div>
            )}

            {/* Store Reviews/Ratings Display */}
            {store.store_reviews && store.store_reviews.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Recent Reviews</h4>
                <div className="space-y-2">
                  {store.store_reviews.slice(0, 2).map((review) => (
                    <div key={review.id} className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-1">
                        <div className="flex items-center gap-1">
                          {[...Array(5)].map((_, i) => (
                            <Star key={i} className={`w-3 h-3 ${i < review.rating ? 'text-rating-gold fill-current' : 'text-gray-300'}`} />
                          ))}
                        </div>
                        {review.verified_purchase && (
                          <CheckCircle className="w-3 h-3 text-wellness" />
                        )}
                      </div>
                      <p className="text-xs text-gray-600 line-clamp-2">{review.review_text}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Contact Info */}
            <div className="flex items-center justify-between pt-3 border-t border-muted">
              <div className="flex items-center gap-1 flex-wrap">
                {store.phone && (
                  <a
                    href={`tel:${store.phone}`}
                    className="flex items-center gap-1 text-xs bg-muted/30 text-foreground px-2 py-1 rounded-md hover:bg-muted/50 transition-colors font-normal group"
                    title={`Call ${store.name} at ${store.phone}`}
                    onClick={() => {
                      // Track call clicks for analytics
                      console.log('Store call clicked:', store.name, store.phone);
                    }}
                  >
                    <Phone className="w-3 h-3 group-hover:animate-pulse" />
                    <span className="hidden sm:inline">Call Store</span>
                    <span className="sm:hidden">{store.phone}</span>
                  </a>
                )}
                {store.website && (
                  <a
                    href={store.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 text-xs bg-muted/30 text-foreground px-2 py-1 rounded-md hover:bg-muted/50 transition-colors font-normal"
                  >
                    <Globe className="w-3 h-3" />
                    Website
                  </a>
                )}
                <a
                  href={`https://maps.google.com/?q=${encodeURIComponent(store.address + ', ' + store.city + ', ' + store.state)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-1 text-xs bg-primary text-background px-2 py-1 rounded-md hover:bg-primary/90 transition-colors font-normal"
                >
                  <MapPin className="w-4 h-4" />
                  Get Directions
                </a>
                <button
                  className="flex items-center gap-1 text-sm bg-purple-50 text-purple-700 px-3 py-2 rounded-lg hover:bg-purple-100 transition-colors font-medium"
                  title="Compare this store"
                >
                  <Heart className="w-4 h-4" />
                  Compare
                </button>
                <button
                  className="flex items-center gap-1 text-sm bg-red-50 text-red-700 px-3 py-2 rounded-lg hover:bg-red-100 transition-colors font-medium"
                  title="Add to favorites"
                  onClick={() => {
                    console.log('Store favorited:', store.name);
                    // In production, would save to user favorites
                  }}
                >
                  <Heart className="w-4 h-4" />
                  Favorite
                </button>
              </div>
            </div>
          </div>
        ))}
        </div>
      </div>
      </main>
    </div>
  );
};

export default StoreLocator;
