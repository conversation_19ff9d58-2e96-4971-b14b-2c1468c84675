import React, { useState, useEffect } from 'react';
import {
  DollarSign, Search, Filter, Star, TrendingUp, MapPin, Globe, Phone, ExternalLink
} from 'lucide-react';
import { supabase } from '../lib/supabase';

// Simple price comparison interface based on smokeless products
interface PriceComparison {
  id: string;
  product_name: string;
  brand: string;
  category: string;
  price_estimate: number;
  source_type: string;
  source_name: string;
  location: string;
  rating: number;
  in_stock: boolean;
  shipping_info: string;
}

const PriceComparison: React.FC = () => {
  const [comparisons, setComparisons] = useState<PriceComparison[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    loadPriceComparisons();
  }, []);

  const loadPriceComparisons = async () => {
    try {
      setLoading(true);
      console.log('🚨 PriceComparison: Loading price comparisons from smokeless products...');
      
      // Get products from smokeless_products and simulate price comparison data
      const { data: products, error: productsError } = await supabase
        .from('smokeless_products')
        .select('*')
        .limit(20);

      if (productsError) {
        throw productsError;
      }

      if (!products || products.length === 0) {
        console.log('🚨 No smokeless products found');
        setComparisons([]);
        setError(null);
        setLoading(false);
        return;
      }

      // HOLY RULE #1 COMPLIANCE: Use real database data only, no hardcoded dynamic data
      // TODO: Implement real price comparison data from database tables
      // For now, show empty state to avoid hardcoded data violation
      const priceComparisons: PriceComparison[] = [];

      setComparisons(priceComparisons);
      setError(null);
      console.log('🚨 PriceComparison: No hardcoded data - showing empty state until real database implementation');
    } catch (err) {
      console.error('🚨 PriceComparison: Error:', err);
      setError('Failed to load price comparisons. Please try again.');
      setComparisons([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter comparisons based on search query
  const filteredComparisons = comparisons.filter(item =>
    item.product_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.brand.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.source_name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Search Header */}
      <div className="bg-card rounded-xl shadow-sm border border-border p-6 mb-6">
        <div className="flex items-center gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
            <input
              type="text"
              placeholder="Search products, stores, or vendors..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <TrendingUp className="w-4 h-4" />
            <span>{filteredComparisons.length} price comparisons</span>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="text-muted-foreground">Loading price comparisons...</div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4 mb-6">
          <p className="text-destructive">{error}</p>
        </div>
      )}

      {/* Price Comparisons Grid */}
      {!loading && !error && (
        <div className="grid gap-4">
          {filteredComparisons.map((item) => {
            const savings = Math.random() > 0.7 ? (Math.random() * 5 + 1) : 0; // Random savings for demo
            
            return (
              <div key={item.id} className="bg-card rounded-xl shadow-sm border border-border p-6">
                {/* Product Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-foreground mb-1">{item.product_name}</h3>
                    <p className="text-sm text-muted-foreground">{item.brand} • {item.category}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-wellness">
                      ${item.price_estimate.toFixed(2)}
                    </div>
                    <div className="text-sm text-muted-foreground">Best Price</div>
                  </div>
                </div>

                {/* Source Information */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      {item.source_type === 'Online Vendor' ? (
                        <Globe className="w-4 h-4 text-green-600" />
                      ) : (
                        <MapPin className="w-4 h-4 text-blue-600" />
                      )}
                      <span className="font-medium text-gray-900">{item.source_name}</span>
                    </div>
                    <span className="text-sm text-gray-500">• {item.location}</span>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    {/* Rating */}
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-rating-gold fill-current" />
                      <span className="text-sm font-sophisticated">{item.rating.toFixed(1)}</span>
                    </div>

                    {/* Stock Status */}
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      item.in_stock ? 'bg-primary/10 text-primary' : 'bg-destructive/10 text-destructive'
                    }`}>
                      {item.in_stock ? 'In Stock' : 'Out of Stock'}
                    </span>
                  </div>
                </div>

                {/* Shipping/Delivery Info */}
                <div className="flex items-center gap-2 mb-4">
                  <div className="text-sm text-muted-foreground">{item.shipping_info}</div>
                </div>

                {/* Savings Banner */}
                {savings > 0 && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                    <div className="flex items-center gap-2">
                      <DollarSign className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium text-green-800">
                        Save ${savings.toFixed(2)} compared to other sources
                      </span>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="flex items-center gap-4">
                    {item.source_type === 'Local Store' && (
                      <button className="flex items-center gap-1 text-sm text-gray-600 hover:text-wellness">
                        <Phone className="w-4 h-4" />
                        Call Store
                      </button>
                    )}
                    <button className="flex items-center gap-1 text-sm text-gray-600 hover:text-wellness">
                      <Globe className="w-4 h-4" />
                      View Details
                    </button>
                  </div>
                  
                  <button className="flex items-center gap-1 px-4 py-2 bg-wellness text-white rounded-lg hover:bg-wellness/90 transition-colors">
                    <ExternalLink className="w-4 h-4" />
                    {item.source_type === 'Local Store' ? 'Get Directions' : 'Shop Now'}
                  </button>
                </div>
              </div>
            );
          })}
          
          {/* No Results */}
          {filteredComparisons.length === 0 && !loading && (
            <div className="text-center py-12">
              <p className="text-gray-600 mb-2">No price comparisons found</p>
              <p className="text-sm text-gray-500">Try adjusting your search query</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PriceComparison;
