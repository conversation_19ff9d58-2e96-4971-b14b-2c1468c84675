import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Store, Search, Filter, Star, Truck, Shield, Award, TrendingUp,
  DollarSign, Package, Clock, CheckCircle, Globe, Phone, MapPin, Mail,
  Heart, Zap, Users, Tag, Gift, Crown, AlertCircle, ExternalLink, AlertTriangle
} from 'lucide-react';
import { getVendors } from '../lib/supabase';
import { DataErrorBoundary } from './ErrorBoundary';

interface Vendor {
  id: string;
  name: string;
  description?: string;
  website?: string;
  phone?: string;
  email?: string;
  address?: string;
  logo_url?: string;
  rating?: number;
  review_count?: number;
  verified?: boolean;
  premium?: boolean;
  minimum_order_amount?: number;
  vendor_inventory?: VendorInventory[];
  vendor_catalog?: VendorCatalog[];
  vendor_reviews?: VendorReview[];
  vendor_shipping?: VendorShipping[];
  created_at: string;
}

interface VendorInventory {
  id: string;
  product_id: string;
  product_name: string;
  brand: string;
  category: string;
  flavors_available: string[];
  nicotine_strengths_available: string[];
  package_sizes: string[];
  in_stock: boolean;
  stock_level: string;
  estimated_delivery_days: number;
  bulk_available: boolean;
  subscription_available: boolean;
}

interface VendorCatalog {
  id: string;
  product_id: string;
  product_name: string;
  brand: string;
  category: string;
  flavor?: string;
  nicotine_strength?: string;
  package_size?: string;
  price_regular: number;
  price_sale?: number;
  price_bulk?: number;
  price_subscription?: number;
  shipping_cost: number;
  free_shipping_threshold: number;
  affiliate_link?: string;
  product_url?: string;
}

interface VendorReview {
  id: string;
  rating: number;
  review_text?: string;
  review_title?: string;
  helpful_count: number;
  verified_purchase: boolean;
  pros: string[];
  cons: string[];
  would_recommend: boolean;
}

interface VendorShipping {
  id: string;
  shipping_method: string;
  shipping_cost: number;
  estimated_days_min: number;
  estimated_days_max: number;
  free_threshold: number;
  tracking_available: boolean;
}

interface VendorFilters {
  searchQuery: string;
  productId: string;
  category: string;
  brand: string;
  flavor: string;
  nicotineStrength: string;
  sortBy: 'price' | 'rating' | 'shipping' | 'availability';
  priceRange: { min: number; max: number };
  inStockOnly: boolean;
  verifiedOnly: boolean;
  freeShippingOnly: boolean;
}

interface VendorDirectoryProps {
  productId?: string;
  category?: string;
}

const VendorDirectory: React.FC<VendorDirectoryProps> = ({ productId, category }) => {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  // Handle add to favorites
  const handleAddToFavorites = async (vendorId: string) => {
    try {
      // TODO: Implement favorites functionality with Supabase
      console.log('Adding vendor to favorites:', vendorId);
      // For now, just show success message
      alert('Vendor added to favorites!');
    } catch (error) {
      console.error('Error adding to favorites:', error);
      alert('Failed to add to favorites');
    }
  };
  
  // Sophisticated filters state
  const [filters, setFilters] = useState<VendorFilters>({
    searchQuery: '',
    productId: productId || '',
    category: category || '',
    brand: '',
    flavor: '',
    nicotineStrength: '',
    sortBy: 'rating',
    priceRange: { min: 0, max: 200 },
    inStockOnly: false,
    verifiedOnly: false,
    freeShippingOnly: false
  });

  // Fetch sophisticated vendors - Initial load
  useEffect(() => {
    console.log('🚨🚨🚨 VendorDirectory: useEffect triggered - Initial vendor load - COMPONENT MOUNTING');
    console.log('🚨🚨🚨 VendorDirectory: About to call fetchVendors - CRITICAL DEBUG');
    fetchVendors();
    console.log('🚨🚨🚨 VendorDirectory: fetchVendors called - AFTER CALL DEBUG');
  }, []); // Empty dependency array for initial load

  // Re-fetch when filters change
  useEffect(() => {
    console.log('🚨🚨🚨 VendorDirectory: Filter change useEffect - filters:', filters);
    if (filters.searchQuery || filters.verifiedOnly || filters.category) {
      console.log('🚨🚨🚨 VendorDirectory: useEffect triggered - Filter change - CALLING FETCHVENDORS');
      fetchVendors();
    } else {
      console.log('🚨🚨🚨 VendorDirectory: Filter change useEffect - NO CONDITIONS MET, NOT CALLING FETCHVENDORS');
    }
  }, [filters.searchQuery, filters.verifiedOnly, filters.category]);

  const fetchVendors = async () => {
    console.log('🚨🚨🚨 FETCHVENDORS: FUNCTION ENTERED - START OF EXECUTION');
    try {
      console.log('🚨🚨🚨 FETCHVENDORS: Setting loading to true');
      setLoading(true);
      setError(null);
      console.log('🚨🚨🚨 FETCHVENDORS: Fetching vendors with filters:', filters);

      // HOLY RULE #1 COMPLIANT: Only use REAL database data - NO SEEDING FAKE DATA

      console.log('🚨🚨🚨 FETCHVENDORS: About to call getVendors from Supabase');
      const vendors = await getVendors();
      console.log('🚨🚨🚨 FETCHVENDORS: getVendors returned:', vendors.length, 'vendors');
      
      // Apply additional filters
      let filteredVendors = vendors;
      
      if (filters.verifiedOnly) {
        filteredVendors = filteredVendors.filter(vendor => vendor.verified);
      }
      
      // Apply search query filter
      if (filters.searchQuery.trim()) {
        const query = filters.searchQuery.toLowerCase();
        filteredVendors = filteredVendors.filter(vendor => 
          vendor.name.toLowerCase().includes(query) ||
          vendor.description?.toLowerCase().includes(query)
        );
      }
      
      console.log('🚨🚨🚨 FETCHVENDORS: Setting vendors state with', filteredVendors.length, 'vendors');
      setVendors(filteredVendors);
      setError(null);
      console.log('🚨🚨🚨 FETCHVENDORS: VENDORS STATE SET SUCCESSFULLY - SHOULD RENDER CARDS NOW');
      console.log('🚨🚨🚨 FETCHVENDORS: Filtered vendors list:', filteredVendors);
    } catch (err) {
      console.error('🚨🚨🚨 FETCHVENDORS: ERROR CAUGHT:', err);
      setError('Failed to load vendors. Please try again.');
      setVendors([]);
    } finally {
      console.log('🚨🚨🚨 FETCHVENDORS: Setting loading to false - FUNCTION COMPLETE');
      setLoading(false);
    }
    console.log('🚨🚨🚨 FETCHVENDORS: FUNCTION ENDED - EXECUTION COMPLETE');
  };

  const updateFilter = (key: keyof VendorFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const getStockLevelColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-wellness bg-background border border-wellness';
      case 'medium': return 'text-rating-gold bg-background border border-rating-gold';
      case 'low': return 'text-rating-gold bg-background border border-rating-gold';
      case 'out_of_stock': return 'text-alert-red bg-background border border-alert-red';
      case 'limited': return 'text-rating-gold bg-background border border-rating-gold';
      default: return 'text-gray bg-background border border-wellness';
    }
  };

  const getLowestPrice = (vendor: Vendor) => {
    if (!vendor.vendor_catalog || vendor.vendor_catalog.length === 0) return null;
    try {
      return Math.min(...vendor.vendor_catalog.map(cat => cat.price_regular));
    } catch (error) {
      return null;
    }
  };

  const getAverageRating = (vendor: Vendor) => {
    if (!vendor.vendor_reviews || vendor.vendor_reviews.length === 0) return vendor.rating || 0;
    return vendor.vendor_reviews.reduce((sum, rev) => sum + rev.rating, 0) / vendor.vendor_reviews.length;
  };

  const getVendorComplianceType = (vendor: Vendor): 'fda-only' | 'smokeless-only' | 'mixed' | 'unknown' => {
    if (!vendor.vendor_inventory || vendor.vendor_inventory.length === 0) {
      return 'unknown';
    }

    const fdaApprovedBrands = ['nicorette', 'nicoderm', 'habitrol', 'commit'];
    const fdaApprovedKeywords = ['patch', 'gum', 'lozenge', 'inhaler', 'nasal spray'];

    let hasFDAProducts = false;
    let hasSmokelessProducts = false;

    vendor.vendor_inventory.forEach(item => {
      const brandLower = item.brand.toLowerCase();
      const productLower = item.product_name.toLowerCase();
      const categoryLower = item.category.toLowerCase();

      const isFDAApproved = fdaApprovedBrands.some(brand => brandLower.includes(brand)) ||
                           fdaApprovedKeywords.some(keyword => productLower.includes(keyword) || categoryLower.includes(keyword));

      if (isFDAApproved) {
        hasFDAProducts = true;
      } else {
        hasSmokelessProducts = true;
      }
    });

    if (hasFDAProducts && hasSmokelessProducts) return 'mixed';
    if (hasFDAProducts) return 'fda-only';
    if (hasSmokelessProducts) return 'smokeless-only';
    return 'unknown';
  };

  const getFastestShipping = (vendor: Vendor) => {
    if (!vendor.vendor_shipping || vendor.vendor_shipping.length === 0) return null;
    try {
      return Math.min(...vendor.vendor_shipping.map(ship => ship.estimated_days_min));
    } catch (error) {
      return null;
    }
  };

  const getCheapestShipping = (vendor: Vendor) => {
    if (!vendor.vendor_shipping || vendor.vendor_shipping.length === 0) return null;
    try {
      return Math.min(...vendor.vendor_shipping.map(ship => ship.shipping_cost));
    } catch (error) {
      return null;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Sophisticated Search Header */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Main Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                id="vendorSearch"
                name="vendorSearch"
                type="text"
                placeholder="Search vendors, products, or brands..."
                value={filters.searchQuery}
                onChange={(e) => updateFilter('searchQuery', e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                aria-label="Search vendors, products, or brands"
              />
            </div>
          </div>
          
          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2 px-4 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            aria-label={showFilters ? "Hide advanced filters" : "Show advanced filters"}
            aria-expanded={showFilters}
            aria-controls="vendor-filters"
          >
            <Filter className="w-5 h-5" />
            Advanced Filters
          </button>
        </div>

        {/* Advanced Filters Panel */}
        {showFilters && (
          <div className="mt-6 pt-6 border-t border-border" id="vendor-filters">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Product Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  value={filters.category}
                  onChange={(e) => updateFilter('category', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                >
                  <option value="">All Categories</option>
                  <option value="gum">Nicotine Gum</option>
                  <option value="lozenges">Lozenges</option>
                  <option value="patches">Patches</option>
                  <option value="pouches">Pouches</option>
                </select>
              </div>

              {/* Brand */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Brand</label>
                <select
                  value={filters.brand}
                  onChange={(e) => updateFilter('brand', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                >
                  <option value="">All Brands</option>
                  <option value="Nicorette">Nicorette</option>
                  <option value="NicoDerm CQ">NicoDerm CQ</option>
                  <option value="Commit">Commit</option>
                  <option value="ZYN">ZYN</option>
                </select>
              </div>

              {/* Price Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Max Price</label>
                <select
                  value={filters.priceRange.max}
                  onChange={(e) => updateFilter('priceRange', { ...filters.priceRange, max: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                >
                  <option value={50}>Under $50</option>
                  <option value={100}>Under $100</option>
                  <option value={150}>Under $150</option>
                  <option value={200}>Under $200</option>
                  <option value={999}>Any Price</option>
                </select>
              </div>

              {/* Sort By */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => updateFilter('sortBy', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                >
                  <option value="rating">Highest Rated</option>
                  <option value="price">Lowest Price</option>
                  <option value="shipping">Fastest Shipping</option>
                  <option value="availability">Best Availability</option>
                </select>
              </div>
            </div>

            {/* Vendor Features */}
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-3">Vendor Features</label>
              <div className="flex flex-wrap gap-3">
                {[
                  { key: 'inStockOnly', label: 'In Stock Only', icon: Package },
                  { key: 'verifiedOnly', label: 'Verified Vendors', icon: Shield },
                  { key: 'freeShippingOnly', label: 'Free Shipping Available', icon: Truck }
                ].map(({ key, label, icon: Icon }) => (
                  <label key={key} className="flex items-center gap-2 cursor-pointer">
                    <input
                      id={`vendorFilter_${key}`}
                      name={`vendorFilter_${key}`}
                      type="checkbox"
                      checked={filters[key as keyof typeof filters] as boolean}
                      onChange={(e) => updateFilter(key as keyof typeof filters, e.target.checked)}
                      className="rounded border-border text-primary focus:ring-primary"
                      aria-describedby={`${key}-description`}
                    />
                    <Icon className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm text-foreground">{label}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-sophisticated text-foreground">
            {loading ? 'Searching...' : `${vendors.length} vendors found`}
          </h2>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-wellness mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Finding the best vendors for you...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <p className="text-red-800">{error}</p>
          </div>
        </div>
      )}

      {/* Vendor Results */}
      {!loading && !error && vendors.length === 0 && (
        <div className="text-center py-12">
          <Store className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No vendors found</h3>
          <p className="text-gray-600">Try adjusting your filters or search criteria.</p>
        </div>
      )}

      {/* Vendor Cards */}
      <DataErrorBoundary>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {vendors.map((vendor) => (
          <Link key={vendor.id} to={`/vendor/${vendor.id}`} className="block">
            <div className="bg-background rounded-xl shadow-sm border border-border p-6 hover:shadow-xl hover:scale-105 hover:-translate-y-1 hover:border-wellness transition-all duration-300 cursor-pointer group">
            {/* Vendor Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-start gap-3 flex-1">
                {/* Vendor Logo/Image */}
                <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center flex-shrink-0">
                  {vendor.logo_url ? (
                    <img
                      src={vendor.logo_url}
                      alt={`${vendor.name} logo`}
                      className="w-10 h-10 object-contain rounded"
                    />
                  ) : (
                    <Store className="w-6 h-6 text-muted-foreground" />
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-lg font-sophisticated text-foreground">{vendor.name}</h3>
                    {vendor.verified && (
                      <CheckCircle className="w-5 h-5 text-wellness" />
                    )}
                    {vendor.premium && (
                      <Crown className="w-5 h-5 text-rating-gold" />
                    )}
                  </div>
                  {vendor.description && (
                    <p className="text-muted-foreground text-sm">{vendor.description}</p>
                  )}
                  {/* Enhanced Vendor Trust Score with Progress Bar */}
                  <div className="mt-3">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center gap-1">
                        <Shield className="w-4 h-4 text-primary" />
                        <span className="text-sm font-sophisticated text-foreground">Trust Score</span>
                      </div>
                      <span className="text-sm font-royal text-primary">{((vendor.rating || 0) * 20).toFixed(0)}%</span>
                    </div>
                    {/* Trust Score Progress Bar */}
                    <div className="w-full bg-muted rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          (vendor.rating || 0) * 20 >= 80 ? 'bg-primary' :
                          (vendor.rating || 0) * 20 >= 60 ? 'bg-rating-gold' :
                          (vendor.rating || 0) * 20 >= 40 ? 'bg-accent' : 'bg-destructive'
                        }`}
                        style={{ width: `${(vendor.rating || 0) * 20}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>Poor</span>
                      <span>Excellent</span>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Rating */}
              <div className="text-right">
                <div className="flex items-center gap-1">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="font-medium">{getAverageRating(vendor).toFixed(1)}</span>
                </div>
                <p className="text-xs text-gray-500">{vendor.vendor_reviews?.length || 0} reviews</p>
              </div>
            </div>

            {/* Legal Compliance Indicator */}
            <div className="mb-4">
              {(() => {
                const complianceType = getVendorComplianceType(vendor);
                if (complianceType === 'fda-only') {
                  return (
                    <div className="flex items-center gap-2 px-3 py-2 bg-primary/10 border border-primary/20 rounded-lg">
                      <Shield className="w-4 h-4 text-primary" />
                      <span className="text-sm font-sophisticated text-primary">FDA-Approved NRT Products Only</span>
                    </div>
                  );
                } else if (complianceType === 'smokeless-only') {
                  return (
                    <div className="flex items-center gap-2 px-3 py-2 bg-destructive/10 border border-destructive/20 rounded-lg">
                      <AlertTriangle className="w-4 h-4 text-destructive" />
                      <span className="text-sm font-sophisticated text-destructive">Non-FDA Smokeless Products Only</span>
                    </div>
                  );
                } else if (complianceType === 'mixed') {
                  return (
                    <div className="flex items-center gap-2 px-3 py-2 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <AlertTriangle className="w-4 h-4 text-yellow-600" />
                      <span className="text-sm font-medium text-yellow-800">Mixed: FDA & Non-FDA Products</span>
                    </div>
                  );
                }
                return null;
              })()}
            </div>

            {/* Enhanced Vendor Features with Specialties */}
            <div className="flex flex-wrap gap-2 mb-4">
              {/* Vendor Specialties/Categories */}
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full font-medium">
                <Package className="w-3 h-3" />
                NRT Specialist
              </span>
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full font-medium">
                <Heart className="w-3 h-3" />
                Quit Support
              </span>

              {vendor.verified && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                  <Shield className="w-3 h-3" />
                  Verified
                </span>
              )}
              {vendor.premium && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                  <Crown className="w-3 h-3" />
                  Premium
                </span>
              )}
              {getFastestShipping(vendor) && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                  <Truck className="w-3 h-3" />
                  {getFastestShipping(vendor)}-day shipping
                </span>
              )}
              {vendor.minimum_order_amount && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                  <DollarSign className="w-3 h-3" />
                  Min ${vendor.minimum_order_amount}
                </span>
              )}
            </div>

            {/* Product Availability */}
            {vendor.vendor_inventory && vendor.vendor_inventory.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-foreground mb-2">Available Products</h4>
                <div className="space-y-2">
                  {vendor.vendor_inventory.slice(0, 3).map((item) => (
                    <div key={item.id} className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-foreground">{item.product_name}</p>
                        <p className="text-xs text-muted-foreground">
                          {item.flavors_available.slice(0, 2).join(', ')} • {item.nicotine_strengths_available.join(', ')}
                        </p>
                      </div>
                      <span className={`px-2 py-1 text-xs rounded-full ${getStockLevelColor(item.stock_level)}`}>
                        {item.stock_level.replace('_', ' ')}
                      </span>
                    </div>
                  ))}
                  {vendor.vendor_inventory.length > 3 && (
                    <p className="text-xs text-muted-foreground">+{vendor.vendor_inventory.length - 3} more products</p>
                  )}
                </div>
              </div>
            )}

            {/* Pricing & Shipping */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              {getLowestPrice(vendor) && (
                <div>
                  <span className="text-sm text-muted-foreground">Starting from</span>
                  <p className="text-lg font-royal text-wellness">${getLowestPrice(vendor)?.toFixed(2)}</p>
                </div>
              )}
              {getCheapestShipping(vendor) !== null && (
                <div>
                  <span className="text-sm text-muted-foreground">Shipping from</span>
                  <p className="text-lg font-royal text-foreground">
                    {getCheapestShipping(vendor) === 0 ? 'FREE' : `$${getCheapestShipping(vendor)?.toFixed(2)}`}
                  </p>
                </div>
              )}
            </div>

            {/* Shipping Cost Calculator Widget */}
            <div className="bg-blue-50 rounded-lg p-4 mb-4">
              <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center gap-2">
                <Truck className="w-4 h-4 text-blue-600" />
                Calculate Shipping Cost
              </h4>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">ZIP Code</label>
                  <input
                    name="zipCode"
                    type="text"
                    placeholder="12345"
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Order Total</label>
                  <input
                    name="orderTotal"
                    type="number"
                    placeholder="50.00"
                    className="w-full px-2 py-1 text-sm border border-border rounded focus:ring-1 focus:ring-primary focus:border-primary"
                  />
                </div>
              </div>
              <button className="w-full mt-3 bg-primary text-primary-foreground py-2 px-4 rounded text-sm hover:bg-primary/90 transition-colors">
                Calculate Shipping
              </button>
              <div className="mt-2 text-xs text-muted-foreground">
                💡 Free shipping on orders over ${vendor.free_shipping_threshold || 75}
              </div>
            </div>

            {/* Contact Info */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Enhanced Contact Information */}
                <div className="space-y-2">
                  <h5 className="text-sm font-medium text-gray-900">Contact Information</h5>
                  {vendor.phone && (
                    <a href={`tel:${vendor.phone}`} className="flex items-center gap-2 text-sm bg-blue-50 text-blue-700 px-3 py-2 rounded-lg hover:bg-blue-100 transition-colors">
                      <Phone className="w-4 h-4" />
                      <div>
                        <span className="font-medium">Call Now</span>
                        <p className="text-xs">{vendor.phone}</p>
                      </div>
                    </a>
                  )}
                  {vendor.email && (
                    <a href={`mailto:${vendor.email}`} className="flex items-center gap-2 text-sm bg-gray-50 text-gray-700 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors">
                      <Mail className="w-4 h-4" />
                      <div>
                        <span className="font-medium">Email</span>
                        <p className="text-xs">{vendor.email}</p>
                      </div>
                    </a>
                  )}
                  {vendor.address && (
                    <div className="flex items-center gap-2 text-sm text-gray-600 px-3 py-2">
                      <MapPin className="w-4 h-4" />
                      <div>
                        <span className="font-medium">Address</span>
                        <p className="text-xs">{vendor.address}</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Quick Actions */}
                <div className="space-y-2">
                  <h5 className="text-sm font-medium text-gray-900">Quick Actions</h5>
                  {vendor.website && (
                    <a href={vendor.website} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2 text-sm bg-wellness-100 text-wellness px-3 py-2 rounded-lg hover:bg-wellness-200 transition-colors">
                      <Globe className="w-4 h-4" />
                      <span className="font-medium">Visit Website</span>
                    </a>
                  )}
                  <a href={`/vendor/${vendor.id}`} className="flex items-center gap-2 text-sm bg-gray-100 text-gray-700 px-3 py-2 rounded-lg hover:bg-gray-200 transition-colors w-full">
                    <ExternalLink className="w-4 h-4" />
                    <span className="font-medium">View Full Catalog</span>
                  </a>
                  <button
                    onClick={() => handleAddToFavorites(vendor.id)}
                    className="flex items-center gap-2 text-sm bg-alert-red-100 text-alert-red px-3 py-2 rounded-lg hover:bg-alert-red-200 transition-colors w-full"
                  >
                    <Heart className="w-4 h-4" />
                    <span className="font-medium">Add to Favorites</span>
                  </button>
                </div>
              </div>
            </div>
            </div>
          </Link>
          ))}
        </div>
      </DataErrorBoundary>
    </div>
  );
};

export default VendorDirectory;
