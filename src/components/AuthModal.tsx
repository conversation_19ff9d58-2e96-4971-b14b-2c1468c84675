import React, { useState, useRef, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'

interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
  mode?: 'signin' | 'signup'
  onToggleMode?: () => void
}

const AuthModal: React.FC<AuthModalProps> = ({ isOpen, onClose, mode = 'signin' }) => {
  const { signIn, signUp } = useAuth()
  const [currentMode, setCurrentMode] = useState<'signin' | 'signup'>(mode)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [fullName, setFullName] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [emailError, setEmailError] = useState('')
  const [passwordError, setPasswordError] = useState('')
  const [successMessage, setSuccessMessage] = useState('')
  const modalRef = useRef<HTMLDivElement>(null)

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
      // Focus the modal when it opens
      modalRef.current?.focus()
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen, onClose])

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!email) {
      setEmailError('Email address is required to access your NRT tracking and preferences')
      return false
    }
    if (!emailRegex.test(email)) {
      setEmailError('Please enter a valid email address (e.g., <EMAIL>)')
      return false
    }
    setEmailError('')
    return true
  }

  const validatePassword = (password: string) => {
    if (!password) {
      setPasswordError('Password is required to secure your NRT journey data')
      return false
    }
    if (password.length < 6) {
      setPasswordError('Password must be at least 6 characters for account security')
      return false
    }
    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
      setPasswordError('Password must include uppercase letter, lowercase letter, and number for security')
      return false
    }
    setPasswordError('')
    return true
  }

  const handleEmailBlur = () => {
    if (email) validateEmail(email)
  }

  const handlePasswordBlur = () => {
    if (password) validatePassword(password)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setSuccessMessage('')

    // Validate form fields
    const isEmailValid = validateEmail(email)
    const isPasswordValid = validatePassword(password)

    if (!isEmailValid || !isPasswordValid) {
      return
    }

    setLoading(true)

    try {
      console.log('🚨 AuthModal: Starting authentication process...', currentMode)
      if (currentMode === 'signup') {
        console.log('🚨 AuthModal: Attempting signup for:', email)
        await signUp(email, password, { full_name: fullName })
        console.log('🚨 AuthModal: Signup successful')
        setSuccessMessage('Welcome to NRTList! Account created successfully. Please check your email to verify your account and start tracking your NRT journey.')
        setTimeout(() => {
          onClose()
        }, 2000)
      } else {
        console.log('🚨 AuthModal: Attempting signin for:', email)
        const result = await signIn(email, password)
        console.log('🚨 AuthModal: Signin result:', result ? 'Success' : 'No result')
        console.log('🚨 AuthModal: Auth localStorage after signin:', localStorage.getItem('nrt-directory-auth'))
        setSuccessMessage('Welcome back! Signed in successfully. Access your NRT preferences, saved products, and progress tracking.')
        setTimeout(() => {
          onClose()
        }, 1000)
      }
    } catch (error: any) {
      console.log('🚨 AuthModal: Authentication error:', error)
      if (error.message.includes('Invalid login credentials')) {
        setError('Invalid email or password. Please double-check your credentials and try again. Need help? Contact support.')
      } else if (error.message.includes('Email not confirmed')) {
        setError('Please check your email inbox and click the verification link to activate your NRT tracking account before signing in.')
      } else if (error.message.includes('User already registered')) {
        setError('An account with this email already exists in our NRT community. Please use the Sign In option instead.')
      } else {
        setError(error.message || 'An unexpected error occurred while accessing your NRT account. Please try again or contact support.')
      }
    } finally {
      setLoading(false)
    }
  }

  const switchMode = () => {
    setCurrentMode(currentMode === 'signin' ? 'signup' : 'signin')
    setError('')
    setEmailError('')
    setPasswordError('')
    setSuccessMessage('')
  }

  if (!isOpen) return null

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={(e) => {
        // Close modal when clicking backdrop
        if (e.target === e.currentTarget) {
          console.log('Closing modal via backdrop click');
          onClose();
        }
      }}
    >
      <div
        ref={modalRef}
        className="bg-background rounded-xl shadow-xl max-w-md w-full p-8 border border-border"
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
        tabIndex={-1}
      >
        <div className="flex justify-between items-center mb-8">
          <div>
            <h2 id="modal-title" className="text-2xl font-royal text-foreground">
              {currentMode === 'signin' ? 'Welcome Back' : 'Join NRTList'}
            </h2>
            <p className="text-sm text-muted-foreground mt-1">
              {currentMode === 'signin'
                ? 'Access your NRT preferences, saved products, and progress tracking'
                : 'Start your personalized NRT journey with expert guidance and community support'
              }
            </p>
          </div>
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClose();
            }}
            className="text-muted-foreground hover:text-foreground text-2xl cursor-pointer z-10 relative p-2 hover:bg-muted rounded-xl transition-all hover:scale-110"
            type="button"
            aria-label="Close modal"
          >
            ×
          </button>
        </div>

        {error && (
          <div className="bg-destructive/10 border border-destructive/30 rounded-xl p-4 mb-6">
            <p className="text-destructive text-sm font-medium">{error}</p>
          </div>
        )}

        {successMessage && (
          <div className="bg-success/10 border border-success/30 rounded-xl p-4 mb-6">
            <p className="text-success text-sm font-medium">{successMessage}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {currentMode === 'signup' && (
            <div>
              <label htmlFor="fullName" className="block text-sm font-medium text-foreground mb-2">
                Full Name <span className="text-muted-foreground text-xs">(for personalized NRT recommendations)</span>
              </label>
              <input
                id="fullName"
                type="text"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                className="w-full px-4 py-3 border border-border rounded-xl focus:outline-none focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus:border-primary transition-all"
                placeholder="Enter your full name (e.g., John Smith)"
                aria-required="true"
              />
            </div>
          )}

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-foreground mb-2">
              Email Address <span className="text-muted-foreground text-xs">(for account access & NRT updates)</span>
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => {
                const value = e.target.value;
                setEmail(value);
                // Clear email error when user starts typing
                if (emailError && value.length > 0) {
                  setEmailError('');
                }
              }}
              onBlur={handleEmailBlur}
              autoComplete="email"
              aria-describedby={emailError ? "email-error" : undefined}
              aria-required="true"
              aria-invalid={!!emailError}
              className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-4 focus:ring-primary focus:ring-opacity-20 transition-all ${
                emailError ? 'border-destructive focus:border-destructive' : 'border-border focus:border-primary'
              }`}
              placeholder="Enter your email address (e.g., <EMAIL>)"
            />
            {emailError && (
              <p id="email-error" className="text-destructive text-xs mt-2 font-medium">{emailError}</p>
            )}
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-foreground mb-2">
              Password {currentMode === 'signup' && <span className="text-muted-foreground text-xs">(secure your NRT data: min 6 chars, uppercase, lowercase, number)</span>}
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              onBlur={handlePasswordBlur}
              minLength={6}
              aria-describedby={passwordError ? "password-error" : undefined}
              aria-required="true"
              aria-invalid={!!passwordError}
              className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-4 focus:ring-primary focus:ring-opacity-20 transition-all ${
                passwordError ? 'border-destructive focus:border-destructive' : 'border-border focus:border-primary'
              }`}
              placeholder={currentMode === 'signup' ? "Create secure password (e.g., MyNRT2024!)" : "Enter your password"}
            />
            {passwordError && (
              <p id="password-error" className="text-destructive text-xs mt-2 font-medium">{passwordError}</p>
            )}
          </div>

          <button
            type="submit"
            disabled={loading || !!emailError || !!passwordError}
            className="w-full bg-primary text-primary-foreground py-4 px-6 rounded-xl hover:bg-primary/90 focus:outline-none focus:ring-4 focus:ring-primary focus:ring-opacity-20 disabled:opacity-50 disabled:cursor-not-allowed font-semibold transition-all hover:scale-[1.02] flex items-center justify-center gap-3"
          >
            {loading && (
              <div className="w-5 h-5 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin" />
            )}
            {loading
              ? (currentMode === 'signin' ? 'Signing you in...' : 'Creating your NRT account...')
              : (currentMode === 'signin' ? 'Sign In to NRTList' : 'Create My NRT Account')
            }
          </button>

          {currentMode === 'signin' && (
            <div className="text-center mt-4">
              <button
                type="button"
                onClick={() => {
                  setError('')
                  setSuccessMessage('Password recovery will be available soon. For immediate help accessing your NRT account, please contact our support team.')
                }}
                className="text-primary hover:text-primary hover:underline text-sm font-medium transition-colors"
              >
                Forgot your password? Get help accessing your NRT account
              </button>
            </div>
          )}
        </form>

        <div className="mt-8 text-center">
          <p className="text-sm text-muted-foreground">
            {currentMode === 'signin'
              ? "New to NRTList? Join our community of users finding their perfect NRT solution"
              : 'Already part of our NRT community? Welcome back'
            }
            <button
              onClick={switchMode}
              className="ml-2 text-primary hover:text-primary/80 font-medium"
            >
              {currentMode === 'signin' ? 'Create Account' : 'Sign In'}
            </button>
          </p>
        </div>

        <div className="mt-4 text-center">
          <p className="text-xs text-muted-foreground">
            By continuing, you agree to our Terms of Service and Privacy Policy. Your NRT journey data is secure and private.
          </p>
        </div>
      </div>
    </div>
  )
}

export default AuthModal
