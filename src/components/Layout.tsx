import React from 'react';
import { Link } from 'react-router-dom';
import Header from './Header';
import { Package } from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Skip Link for Accessibility */}
      <a href="#main-content" className="skip-link">
        Skip to main content
      </a>

      {/* Universal Header - Always Present */}
      <Header />

      {/* Main Content */}
      <main id="main-content" className="flex-1">
        {children}
      </main>
      
      {/* Apple Mac Desktop Universal Footer */}
      <footer className="bg-card border-t border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-primary rounded-xl flex items-center justify-center">
                  <Package className="w-5 h-5 text-primary-foreground" strokeWidth={2} />
                </div>
                <span className="text-lg font-royal text-foreground">NRTList</span>
              </div>
              <p className="text-muted-foreground leading-relaxed">
                The comprehensive platform for nicotine replacement therapy discovery, comparison,
                and smoking cessation support with verified products and expert guidance.
              </p>
            </div>

            <div>
              <h2 className="font-sophisticated mb-4 text-foreground">Products & Compare</h2>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link to="/nrt" className="hover:text-foreground transition-colors">Browse All NRT Products</Link></li>
                <li><Link to="/nrt" className="hover:text-foreground transition-colors">Product Comparison Tool</Link></li>
                <li><Link to="/price-compare" className="hover:text-foreground transition-colors">Price Comparison</Link></li>
                <li><Link to="/reviews" className="hover:text-foreground transition-colors">Product Reviews</Link></li>
              </ul>
            </div>

            <div>
              <h2 className="font-sophisticated mb-4 text-foreground">Retailers & Support</h2>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link to="/retailers" className="hover:text-foreground transition-colors">Find Verified Retailers</Link></li>
                <li><Link to="/retailers" className="hover:text-foreground transition-colors">Store Locator</Link></li>
                <li><Link to="/community" className="hover:text-foreground transition-colors">Community Support</Link></li>
                <li><Link to="/progress" className="hover:text-foreground transition-colors">Progress Tracking</Link></li>
              </ul>
            </div>

            <div>
              <h2 className="font-sophisticated mb-4 text-foreground">Company & Legal</h2>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link to="/about" className="hover:text-foreground transition-colors">About NRTList</Link></li>
                <li><Link to="/privacy" className="hover:text-foreground transition-colors">Privacy Policy</Link></li>
                <li><Link to="/terms" className="hover:text-foreground transition-colors">Terms of Service</Link></li>
                <li><Link to="/contact" className="hover:text-foreground transition-colors">Contact Support</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-border mt-16 pt-8 text-center">
            <p className="text-muted-foreground">&copy; 2024 NRTList. All rights reserved. Helping people quit smoking with comprehensive NRT resources.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
