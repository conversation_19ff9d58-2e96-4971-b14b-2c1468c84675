import React from 'react';

interface SkeletonLoaderProps {
  variant?: 'text' | 'card' | 'avatar' | 'button' | 'product' | 'testimonial';
  width?: string;
  height?: string;
  className?: string;
  count?: number;
}

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({ 
  variant = 'text', 
  width, 
  height, 
  className = '',
  count = 1 
}) => {
  const baseClasses = 'animate-pulse bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] rounded-lg';
  
  const getVariantClasses = () => {
    switch (variant) {
      case 'text':
        return `h-4 ${width || 'w-full'}`;
      case 'card':
        return `${width || 'w-full'} ${height || 'h-48'}`;
      case 'avatar':
        return `${width || 'w-12'} ${height || 'h-12'} rounded-full`;
      case 'button':
        return `${width || 'w-24'} ${height || 'h-10'}`;
      case 'product':
        return `${width || 'w-full'} ${height || 'h-64'}`;
      case 'testimonial':
        return `${width || 'w-full'} ${height || 'h-32'}`;
      default:
        return `h-4 ${width || 'w-full'}`;
    }
  };

  const skeletonElement = (
    <div 
      className={`${baseClasses} ${getVariantClasses()} ${className}`}
      style={{ width, height }}
    />
  );

  if (count === 1) {
    return skeletonElement;
  }

  return (
    <>
      {Array.from({ length: count }, (_, index) => (
        <div key={index} className="mb-2">
          {skeletonElement}
        </div>
      ))}
    </>
  );
};

// Specific skeleton components for common use cases
export const ProductCardSkeleton: React.FC = () => (
  <div className="bg-background rounded-2xl shadow-lg border border-border/10 p-6 animate-pulse">
    <div className="bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] h-48 rounded-xl mb-4"></div>
    <div className="space-y-3">
      <div className="bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] h-6 rounded w-3/4"></div>
      <div className="bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] h-4 rounded w-1/2"></div>
      <div className="flex justify-between items-center">
        <div className="bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] h-4 rounded w-1/4"></div>
        <div className="bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] h-4 rounded w-1/3"></div>
      </div>
    </div>
  </div>
);

export const TestimonialSkeleton: React.FC = () => (
  <div className="bg-background rounded-2xl shadow-lg border border-border/10 p-8 animate-pulse">
    <div className="flex items-center mb-4">
      <div className="bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] w-12 h-12 rounded-full mr-4"></div>
      <div className="space-y-2">
        <div className="bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] h-4 rounded w-24"></div>
        <div className="bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] h-3 rounded w-16"></div>
      </div>
    </div>
    <div className="space-y-2">
      <div className="bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] h-4 rounded w-full"></div>
      <div className="bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] h-4 rounded w-5/6"></div>
      <div className="bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] h-4 rounded w-4/6"></div>
    </div>
  </div>
);

export const VendorSkeleton: React.FC = () => (
  <div className="bg-background rounded-2xl shadow-lg border border-border/10 p-6 animate-pulse">
    <div className="flex items-center mb-4">
      <div className="bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] w-16 h-16 rounded-xl mr-4"></div>
      <div className="space-y-2">
        <div className="bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] h-5 rounded w-32"></div>
        <div className="bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] h-3 rounded w-24"></div>
      </div>
    </div>
    <div className="space-y-2">
      <div className="bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] h-4 rounded w-full"></div>
      <div className="bg-gradient-to-r from-muted via-muted-foreground/20 to-muted bg-[length:200%_100%] h-4 rounded w-3/4"></div>
    </div>
  </div>
);

export default SkeletonLoader;
