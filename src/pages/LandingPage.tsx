import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Search, Menu, X, DollarSign, Users, Star, ArrowRight, Package, Target, Store, Award, Shield, CheckCircle } from 'lucide-react';
import { getProducts, getTestimonials, supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import AuthModal from '../components/AuthModal';
import { DataErrorBoundary } from '../components/ErrorBoundary';
import { ProductCardSkeleton, TestimonialSkeleton } from '../components/SkeletonLoader';


const LandingPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated, user, signOut } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin');
  const [products, setProducts] = useState<any[]>([]);
  const [testimonials, setTestimonials] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [newsletterEmail, setNewsletterEmail] = useState('');
  const [newsletterStatus, setNewsletterStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [newsletterMessage, setNewsletterMessage] = useState('');

  // Clean search handler for production - FIXED
  const handleSearch = (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  // Clean search input handler
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Real newsletter signup handler - saves to mission_fresh.newsletter_signups table
  const handleNewsletterSignup = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const email = formData.get('email') as string;

    if (!email || !email.includes('@') || !email.includes('.')) {
      setNewsletterStatus('error');
      setNewsletterMessage('Please enter a valid email address');
      return;
    }

    setNewsletterStatus('loading');
    setNewsletterMessage('');

    try {
      // Real database insert to mission_fresh.newsletter_signups
      const { error } = await supabase
        .from('newsletter_signups')
        .insert([
          {
            email: email.trim().toLowerCase(),
            subscribed_at: new Date().toISOString(),
            status: 'active',
            source: 'landing_page'
          }
        ]);

      if (error) {
        console.error('Newsletter signup error:', error);
        if (error.code === '23505') { // Unique constraint violation
          setNewsletterStatus('error');
          setNewsletterMessage('This email is already subscribed to our newsletter.');
        } else {
          setNewsletterStatus('error');
          setNewsletterMessage('Failed to subscribe. Please try again later.');
        }
      } else {
        setNewsletterStatus('success');
        setNewsletterMessage('Successfully subscribed! Thank you for joining our newsletter.');
        setNewsletterEmail('');
        // Clear success message after 5 seconds
        setTimeout(() => {
          setNewsletterStatus('idle');
          setNewsletterMessage('');
        }, 5000);
      }
    } catch (error) {
      console.error('Newsletter signup error:', error);
      setNewsletterStatus('error');
      setNewsletterMessage('Failed to subscribe. Please try again later.');
    }
  };



  // Load data from database
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const [productData, testimonialsData] = await Promise.all([
          getProducts(),
          getTestimonials()
        ]);

        setProducts(productData);
        setTestimonials(testimonialsData);
        setError(null);
      } catch (error) {
        console.error('Error loading data:', error);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  return (
    <div className="min-h-screen bg-background">
      {/* Apple Mac Desktop Navigation Header - System Preferences Style */}
      <nav className="fixed top-0 left-0 right-0 z-[60] bg-background/98 backdrop-blur-xl border-b border-border/30 shadow-[0_1px_2px_rgba(0,0,0,0.04)]" role="navigation" aria-label="Main navigation">
        <div className="max-w-7xl mx-auto px-6 sm:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo - Apple Mac Desktop Professional Branding - Enhanced Elegance */}
            <div className="flex items-center h-full">
              <Link to="/" className="flex items-center gap-4 hover:opacity-95 transition-all duration-200 group" aria-label="NRTList homepage">
                <div className="w-10 h-10 bg-primary rounded-xl flex items-center justify-center shadow-[0_1px_3px_rgba(0,0,0,0.08)] group-hover:shadow-[0_2px_6px_rgba(0,0,0,0.12)] transition-all duration-200" role="img" aria-label="NRTList logo">
                  <span className="text-white font-royal text-lg leading-none tracking-royal">N</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-xl font-royal text-foreground tracking-royal leading-none">NRTList</span>
                  <span className="text-sm text-muted-foreground font-sophisticated tracking-elegant leading-none mt-1">Nicotine Replacement Directory</span>
                </div>
              </Link>
            </div>

            {/* Apple Mac Desktop Navigation - Streamlined Core Features */}
            <div className="hidden lg:flex items-center space-x-3">
              <Link to="/nrt" className="text-muted-foreground hover:text-foreground transition-all duration-200 font-refined text-sm px-4 py-2 rounded-xl hover:bg-muted/30">
                Products
              </Link>
              <Link to="/stores" className="text-muted-foreground hover:text-foreground transition-all duration-200 font-refined text-sm px-4 py-2 rounded-xl hover:bg-muted/30">
                Stores
              </Link>
              <Link to="/price-compare" className="text-muted-foreground hover:text-foreground transition-all duration-200 font-refined text-sm px-4 py-2 rounded-xl hover:bg-muted/30">
                Compare
              </Link>
              <Link to="/reviews" className="text-muted-foreground hover:text-foreground transition-all duration-200 font-refined text-sm px-4 py-2 rounded-xl hover:bg-muted/30">
                Reviews
              </Link>
              <Link to="/community" className="text-muted-foreground hover:text-foreground transition-all duration-200 font-refined text-sm px-4 py-2 rounded-xl hover:bg-muted/30">
                Community
              </Link>
            </div>

            {/* Apple Mac Desktop Auth Actions */}
            <div className="flex items-center space-x-4">
              <div className="hidden lg:flex items-center space-x-4">
                {isAuthenticated ? (
                  <div className="flex items-center space-x-4">
                    <span className="text-muted-foreground font-sophisticated text-sm">
                      Welcome, {user?.email?.split('@')[0]}
                    </span>
                    <button
                      onClick={signOut}
                      className="text-muted-foreground hover:text-foreground font-sophisticated transition-all duration-200 text-sm px-4 py-2 rounded-xl hover:bg-muted/30 border border-transparent hover:border-border/40"
                    >
                      Sign Out
                    </button>
                  </div>
                ) : (
                  <>
                    <button
                      onClick={() => {
                        setAuthMode('signin');
                        setShowAuthModal(true);
                      }}
                      className="text-muted-foreground hover:text-foreground font-sophisticated transition-all duration-200 text-sm px-4 py-2 rounded-xl hover:bg-muted/30 border border-transparent hover:border-border/40"
                    >
                      Sign In
                    </button>
                    <button
                      onClick={() => {
                        setAuthMode('signup');
                        setShowAuthModal(true);
                      }}
                      className="bg-primary text-primary-foreground px-6 py-3 rounded-xl font-refined hover:bg-primary/90 hover:shadow-[0_2px_8px_rgba(0,0,0,0.12)] active:shadow-[0_1px_4px_rgba(0,0,0,0.16)] transition-all duration-200 text-sm tracking-refined shadow-[0_1px_3px_rgba(0,0,0,0.08)]"
                    >
                      Get Started
                    </button>
                  </>
                )}
              </div>

              {/* Apple Mac Desktop Mobile Menu Button */}
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="md:hidden p-2 hover:bg-muted/50 rounded-lg transition-all duration-150"
                aria-label="Toggle navigation menu"
                aria-expanded={isMenuOpen}
              >
                {isMenuOpen ? (
                  <X className="w-4 h-4 text-foreground" strokeWidth={2} />
                ) : (
                  <Menu className="w-4 h-4 text-foreground" strokeWidth={2} />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu - iOS Style Navigation */}
        {isMenuOpen && (
          <div className="md:hidden bg-background border-t border-muted/50 shadow-[inset_0_1px_2px_rgba(0,0,0,0.03)]">
            <div className="px-4 py-2 space-y-3">
              <Link to="/nrt" className="flex items-center justify-between text-foreground hover:text-primary font-sophisticated py-2.5 border-b border-muted/30">
                NRT Directory <ArrowRight size={16} />
              </Link>
              <Link to="/retailers" className="flex items-center justify-between text-foreground hover:text-primary font-sophisticated py-2.5 border-b border-muted/30">
                Retailers & Stores <ArrowRight size={16} />
              </Link>
              <Link to="/community" className="flex items-center justify-between text-foreground hover:text-primary font-sophisticated py-2.5 border-b border-muted/30">
                Community Support <ArrowRight size={16} />
              </Link>
              <Link to="/progress" className="flex items-center justify-between text-foreground hover:text-primary font-sophisticated py-2.5 border-b border-muted/30">
                Progress Tracking <ArrowRight size={16} />
              </Link>
              <div className="pt-4 border-t border-primary space-y-3">
                {isAuthenticated ? (
                  <>
                    <div className="text-primary font-semibold">
                      Welcome, {user?.email?.split('@')[0]}
                    </div>
                    <button
                      onClick={signOut}
                      className="block w-full text-left text-primary font-sophisticated"
                    >
                      Sign Out
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={() => {
                        setAuthMode('signin');
                        setShowAuthModal(true);
                        setIsMenuOpen(false);
                      }}
                      className="block w-full text-left text-primary font-sophisticated"
                    >
                      Sign In
                    </button>
                    <button
                      onClick={() => {
                        setAuthMode('signup');
                        setShowAuthModal(true);
                        setIsMenuOpen(false);
                      }}
                      className="block w-full text-center bg-primary text-primary-foreground px-6 py-3 rounded-lg font-refined hover:bg-primary/90 hover:shadow-[0_2px_6px_rgba(0,0,0,0.12)] active:shadow-[0_1px_3px_rgba(0,0,0,0.16)] transition-all duration-200 tracking-refined shadow-[0_1px_3px_rgba(0,0,0,0.08)]"
                    >
                      Get Started
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </nav>

      {/* Accessibility Skip Links */}
      <div className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50">
        <a href="#main-content" className="bg-primary text-primary-foreground px-4 py-2 rounded-md">
          Skip to main content
        </a>
      </div>

      {/* Main Content */}
      <main id="main-content" className="min-h-screen bg-background">
        {/* Apple Mac Desktop Hero Section - Enhanced Modern Design */}
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 md:px-8 pt-16 sm:pt-18 md:pt-20 pb-16 sm:pb-20 md:pb-24 overflow-hidden">
          {/* Modern Apple Mac Desktop Background with Subtle Animation */}
          <div className="absolute inset-0 opacity-[0.03] pointer-events-none">
            {/* Animated geometric accents - Apple minimal style */}
            <div className="absolute top-20 right-20 w-px h-32 bg-gradient-to-b from-primary/20 to-transparent animate-pulse"></div>
            <div className="absolute bottom-20 left-20 w-32 h-px bg-gradient-to-r from-primary/20 to-transparent animate-pulse"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-radial from-primary/5 to-transparent rounded-full"></div>
          </div>

          <div className="text-center relative space-y-8">
          {/* Enhanced Apple Mac Desktop Hero Content */}
          <header className="max-w-4xl mx-auto mb-8">
            {/* Single Professional Platform Badge */}
            <div className="inline-flex items-center px-5 py-2 rounded-full bg-primary/8 text-primary text-sm font-medium mb-8 border border-primary/20">
              <CheckCircle className="w-4 h-4 mr-2" />
              <span>Verified NRT Directory Platform</span>
              <span className="mx-2 text-primary/40">•</span>
              <span className="hidden sm:inline">Trusted by healthcare providers</span>
            </div>

            {/* Modern Hero Heading with Clean Typography */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-royal text-foreground tracking-royal leading-tight mt-6 mb-8">
              <span className="block text-foreground/95 font-elegant mb-6">Find Your Perfect </span>
              <span className="block text-primary font-royal tracking-royal">Nicotine Alternative</span>
            </h1>
          </header>

          {/* Apple-Style Action Banner - Enhanced Typography */}
          <div className="max-w-3xl mx-auto mb-8 sm:mb-10 md:mb-12">
            <div
              className="bg-card border border-border/50 px-6 sm:px-8 md:px-10 py-6 sm:py-7 md:py-8 rounded-lg sm:rounded-xl text-center hover:shadow-[0_2px_8px_rgba(0,0,0,0.04)] transition-all duration-200 cursor-pointer group"
              onClick={() => navigate('/retailers')}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  navigate('/retailers');
                }
              }}
            >
              <div className="flex items-center justify-center gap-3 mb-3">
                <div className="w-7 h-7 sm:w-8 sm:h-8 bg-primary/10 rounded-full flex items-center justify-center">
                  <Store className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
                </div>
                <span className="text-lg sm:text-xl font-refined text-foreground tracking-refined">Find NRT Products Nearby</span>
              </div>
              <p className="text-sm sm:text-base text-muted-foreground font-sophisticated tracking-graceful leading-relaxed">
                Discover verified products and trusted stores in your area
              </p>
              </div>
            </div>

          {/* Apple Mac Desktop Subtitle - Enhanced Typography */}
          <div className="max-w-5xl mx-auto mb-8 sm:mb-10 md:mb-12">
            <p className="text-2xl sm:text-3xl md:text-4xl text-muted-foreground font-sophisticated leading-relaxed tracking-refined text-center">
              {loading ? (
                <span>Discover FDA-approved NRT products and smokeless alternatives. Compare prices, read reviews, and make informed decisions for your quit journey.</span>
              ) : products.length > 0 ? (
                <>Discover FDA-approved NRT products and smokeless alternatives. Compare prices across <span className="text-primary font-medium">{products.length}+</span> products, read reviews, and make informed decisions for your quit journey.</>
              ) : (
                <>Discover FDA-approved NRT products and smokeless alternatives. Compare prices, read reviews, and make informed decisions for your quit journey.</>
              )}
            </p>
          </div>

          {/* Apple Mac Desktop Value Proposition - Enhanced Typography */}
          <div className="max-w-3xl mx-auto mb-8 sm:mb-10 md:mb-12">
            <p className="text-xl sm:text-2xl text-foreground font-refined tracking-refined text-center leading-relaxed">
              Professional. Reliable. Comprehensive. Trusted by healthcare providers.
            </p>
          </div>

          {/* Modern Apple Mac Desktop Search Bar with Enhanced Interactions */}
          <div className="max-w-4xl mx-auto mb-12 sm:mb-14 md:mb-16">
            <form onSubmit={handleSearch} className="relative group" role="search">
              {/* Enhanced Search Icon with Animation */}
              <div className="absolute inset-y-0 left-0 pl-5 sm:pl-6 flex items-center pointer-events-none">
                <Search className="h-6 w-6 sm:h-7 sm:w-7 text-muted-foreground/60 group-focus-within:text-primary/80 transition-colors duration-300" />
              </div>
              {/* Modern Input Field with Enhanced Design */}
              <input
                type="text"
                value={searchQuery}
                onChange={handleSearchInputChange}
                placeholder="Find Nicotine Gum, Patches, Lozenges & More..."
                className="block w-full pl-14 sm:pl-16 pr-32 sm:pr-36 py-5 sm:py-6 text-lg sm:text-xl font-sophisticated border border-border/40 rounded-2xl sm:rounded-3xl bg-background/98 backdrop-blur-md focus:outline-none focus:ring-2 focus:ring-primary/40 focus:border-primary/50 focus:bg-background transition-all duration-300 placeholder-muted-foreground/50 hover:border-primary/30 hover:shadow-[0_4px_16px_rgba(0,0,0,0.06)] shadow-[0_2px_8px_rgba(0,0,0,0.04)] group-focus-within:shadow-[0_6px_20px_rgba(0,0,0,0.08)]"
                aria-label="Search for NRT products and stores"
              />
              {/* Enhanced Search Button with Modern Design */}
              <div className="absolute inset-y-0 right-0 pr-4 sm:pr-5 flex items-center">
                <button
                  type="submit"
                  className="bg-primary text-primary-foreground px-6 sm:px-8 py-3 sm:py-4 rounded-xl sm:rounded-2xl hover:bg-primary/90 hover:shadow-[0_4px_12px_rgba(34,197,94,0.25)] active:shadow-[0_2px_6px_rgba(34,197,94,0.35)] transition-all duration-300 font-refined text-base sm:text-lg tracking-refined shadow-[0_2px_8px_rgba(34,197,94,0.15)] hover:scale-105 active:scale-95"
                >
                  Search
                </button>
              </div>
            </form>
          </div>

          {/* Secondary Action Cards - Supporting Features */}
          <div className="max-w-4xl mx-auto mb-24 sm:mb-28 md:mb-32">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 sm:gap-10">
                {/* Store Finder CTA - Modern Enhanced Design */}
                <Link
                  to="/retailers"
                  className="group bg-background/95 backdrop-blur-md border border-border/30 rounded-2xl sm:rounded-3xl p-10 sm:p-12 hover:shadow-[0_8px_24px_rgba(0,0,0,0.08)] hover:border-primary/40 hover:bg-background/98 transition-all duration-500 text-center flex flex-col items-center gap-8 hover:scale-[1.02] active:scale-[0.98]"
                  aria-label="Find stores near you that sell NRT products"
                >
                  <div className="w-16 h-16 sm:w-18 sm:h-18 bg-gradient-to-br from-primary/15 to-primary/5 rounded-3xl flex items-center justify-center group-hover:bg-gradient-to-br group-hover:from-primary/20 group-hover:to-primary/8 transition-all duration-500 shadow-[0_4px_12px_rgba(34,197,94,0.12)] group-hover:shadow-[0_6px_16px_rgba(34,197,94,0.18)] group-hover:scale-110">
                    <Store className="w-8 h-8 sm:w-9 sm:h-9 text-primary group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <div className="space-y-4">
                    <div className="text-2xl sm:text-3xl font-royal text-foreground tracking-royal">Find Store Near Me</div>
                    <div className="text-lg sm:text-xl text-muted-foreground font-sophisticated leading-relaxed">Locate nearby stores with real-time inventory</div>
                  </div>
                </Link>

                {/* Price Compare CTA - Modern Enhanced Design */}
                <Link
                  to="/price-compare"
                  className="group bg-background/95 backdrop-blur-md border border-border/30 rounded-2xl sm:rounded-3xl p-10 sm:p-12 hover:shadow-[0_8px_24px_rgba(0,0,0,0.08)] hover:border-primary/40 hover:bg-background/98 transition-all duration-500 text-center flex flex-col items-center gap-8 hover:scale-[1.02] active:scale-[0.98]"
                  aria-label="Compare prices across vendors for best NRT deals"
                >
                  <div className="w-16 h-16 sm:w-18 sm:h-18 bg-gradient-to-br from-primary/15 to-primary/5 rounded-3xl flex items-center justify-center group-hover:bg-gradient-to-br group-hover:from-primary/20 group-hover:to-primary/8 transition-all duration-500 shadow-[0_4px_12px_rgba(34,197,94,0.12)] group-hover:shadow-[0_6px_16px_rgba(34,197,94,0.18)] group-hover:scale-110">
                    <DollarSign className="w-8 h-8 sm:w-9 sm:h-9 text-primary group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <div className="space-y-4">
                    <div className="text-2xl sm:text-3xl font-royal text-foreground tracking-royal">Compare Prices Now</div>
                    <div className="text-lg sm:text-xl text-muted-foreground font-sophisticated leading-relaxed">Compare verified vendors & save money</div>
                  </div>
                </Link>


              </div>
            </div>

            {/* Apple Mac Desktop Filter Buttons - Enhanced with Icons */}
            <nav className="max-w-4xl mx-auto mb-16" aria-label="Product categories">
              <div className="flex flex-wrap justify-center gap-4 sm:gap-5">
                <Link
                  to="/nrt"
                  className="group bg-card/70 backdrop-blur-sm border border-border/50 text-muted-foreground px-5 sm:px-6 py-3 sm:py-3.5 rounded-xl sm:rounded-2xl hover:bg-card/90 hover:border-primary/40 hover:text-foreground hover:shadow-[0_3px_8px_rgba(0,0,0,0.08)] transition-all duration-250 font-refined text-sm sm:text-base tracking-refined font-medium"
                  aria-label="Browse all NRT products"
                >
                  All Products
                </Link>
                <Link
                  to="/nrt?category=gum"
                  className="group bg-card/70 backdrop-blur-sm border border-border/50 text-muted-foreground px-5 sm:px-6 py-3 sm:py-3.5 rounded-xl sm:rounded-2xl hover:bg-card/90 hover:border-primary/40 hover:text-foreground hover:shadow-[0_3px_8px_rgba(0,0,0,0.08)] transition-all duration-250 font-refined text-sm sm:text-base tracking-refined font-medium"
                  aria-label="Browse nicotine gum products"
                >
                  Gum
                </Link>
                <Link
                  to="/nrt?category=patch"
                  className="group bg-card/70 backdrop-blur-sm border border-border/50 text-muted-foreground px-5 sm:px-6 py-3 sm:py-3.5 rounded-xl sm:rounded-2xl hover:bg-card/90 hover:border-primary/40 hover:text-foreground hover:shadow-[0_3px_8px_rgba(0,0,0,0.08)] transition-all duration-250 font-refined text-sm sm:text-base tracking-refined font-medium"
                  aria-label="Browse nicotine patch products"
                >
                  Patch
                </Link>
                <Link
                  to="/nrt?category=lozenge"
                  className="group bg-card/70 backdrop-blur-sm border border-border/50 text-muted-foreground px-5 sm:px-6 py-3 sm:py-3.5 rounded-xl sm:rounded-2xl hover:bg-card/90 hover:border-primary/40 hover:text-foreground hover:shadow-[0_3px_8px_rgba(0,0,0,0.08)] transition-all duration-250 font-refined text-sm sm:text-base tracking-refined font-medium"
                  aria-label="Browse nicotine lozenge products"
                >
                  Lozenge
                </Link>
                <Link
                  to="/nrt?category=spray"
                  className="group bg-card/70 backdrop-blur-sm border border-border/50 text-muted-foreground px-5 sm:px-6 py-3 sm:py-3.5 rounded-xl sm:rounded-2xl hover:bg-card/90 hover:border-primary/40 hover:text-foreground hover:shadow-[0_3px_8px_rgba(0,0,0,0.08)] transition-all duration-250 font-refined text-sm sm:text-base tracking-refined font-medium"
                  aria-label="Browse nicotine spray products"
                >
                  Spray
                </Link>
              </div>
            </nav>

            {/* Single Primary CTA - Optimized for Clarity */}
            <div className="flex justify-center items-center mb-16" role="group" aria-label="Primary action">
              <Link
                to="/nrt"
                className="bg-primary text-primary-foreground px-8 py-4 rounded-xl font-semibold hover:bg-primary/90 transition-all duration-200 flex items-center gap-3 text-lg shadow-[0_4px_12px_rgba(0,0,0,0.12)] hover:shadow-[0_6px_16px_rgba(0,0,0,0.16)] hover:scale-[1.02] active:scale-[0.98]"
                aria-label="Browse comprehensive NRT product directory"
              >
                <Package className="w-6 h-6" aria-hidden="true" />
                <span>Browse NRT Products</span>
                <ArrowRight className="w-5 h-5" aria-hidden="true" />
              </Link>
            </div>

            {/* Apple Mac Desktop Trust Indicators */}
            <div className="flex flex-wrap justify-center items-center gap-5 text-sm text-muted-foreground mb-16">
              <div className="flex items-center gap-2.5 bg-muted/70 px-4 py-2 rounded-lg border border-border/30">
                <Shield className="w-4 h-4 text-primary" />
                <span className="font-medium">FDA-Approved Products</span>
              </div>
              <div className="flex items-center gap-2.5 bg-muted/70 px-4 py-2 rounded-lg border border-border/30">
                <Award className="w-4 h-4 text-primary" />
                <span className="font-medium">Expert Reviewed</span>
              </div>
              <div className="flex items-center gap-2.5 bg-muted/70 px-4 py-2 rounded-lg border border-border/30">
                <Users className="w-4 h-4 text-primary" />
                <span className="font-medium">{loading ? (
                  <span className="inline-block bg-muted rounded w-24 h-4"></span>
                ) : 'Community Reviewed'}</span>
              </div>
            </div>

            {/* Apple Mac Desktop Core Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
              <div className="bg-background rounded-lg p-6 border border-muted text-center hover:shadow-[0_2px_5px_rgba(0,0,0,0.04)] transition-all duration-150 hover:border-accent">
                <div className="w-10 h-10 bg-primary rounded-md flex items-center justify-center mx-auto mb-3">
                  <Search className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-refined text-foreground mb-3 tracking-refined">NRT Store Locator</h3>
                <p className="text-muted-foreground leading-relaxed text-sm font-normal">Find nearest pharmacies, supermarkets & stores that sell NRT when cravings hit. Real-time inventory & pricing.</p>
              </div>

              <div className="bg-background rounded-lg p-6 border border-muted text-center hover:shadow-[0_2px_5px_rgba(0,0,0,0.04)] transition-all duration-150 hover:border-accent">
                <div className="w-12 h-12 bg-secondary rounded-lg flex items-center justify-center mx-auto mb-4">
                  <DollarSign className="w-6 h-6 text-foreground" />
                </div>
                <h3 className="text-lg font-refined text-foreground mb-3 tracking-refined">Online Vendor Price Comparison</h3>
                <p className="text-muted-foreground leading-relaxed text-sm font-normal">Compare prices across {loading ? (
                  <span className="inline-block bg-muted rounded w-12 h-4 align-middle"></span>
                ) : products.length > 0 ? `${products.length}+` : 'available'} NRT products from multiple vendors. Check delivery options & discounts.</p>
              </div>

              <div className="bg-background rounded-lg p-6 border border-muted text-center hover:shadow-[0_2px_5px_rgba(0,0,0,0.04)] transition-all duration-150 hover:border-accent">
                <div className="w-12 h-12 bg-secondary rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Star className="w-6 h-6 text-foreground" />
                </div>
                <h3 className="text-lg font-refined text-foreground mb-3 tracking-refined">Product Reviews & Ratings</h3>
                <p className="text-muted-foreground leading-relaxed text-sm font-normal">Read authentic reviews from real users. Compare effectiveness, taste, and value across {loading ? (
                  <span className="inline-block bg-muted rounded w-12 h-4 align-middle"></span>
                ) : products.length > 0 ? `${products.length}+` : 'available'} NRT products.</p>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Apple Mac Desktop Secondary Section */}
      <section className="py-24 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-elegant text-foreground mb-6 leading-tight tracking-elegant">
              The Vivino for <span className="text-primary">NRT Products</span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed font-normal">
              Discover, rate, and find the best NRT deals with our comprehensive directory and price comparison platform
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-primary rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Search className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-medium text-foreground mb-2">
                  NRT Store Locator
                </h3>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  Find nearest pharmacies, supermarkets & stores that sell NRT when cravings hit. Real-time inventory & pricing.
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-muted rounded-xl flex items-center justify-center mx-auto mb-4">
                  <DollarSign className="w-8 h-8 text-foreground" />
                </div>
                <h3 className="text-lg font-medium text-foreground mb-2">
                  Online Vendor Price Comparison
                </h3>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  Compare prices across {loading ? (
                    <span className="inline-block bg-muted rounded w-12 h-4 align-middle"></span>
                  ) : products.length > 0 ? `${products.length}+` : 'available'} NRT products from multiple vendors. Check delivery options & discounts.
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-muted rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Star className="w-8 h-8 text-foreground" />
                </div>
                <h3 className="text-lg font-medium text-foreground mb-2">
                  Complete NRT Directory
                </h3>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  Professional reviews of every NRT product. Detailed specs: nicotine strength, flavor, effectiveness.
                </p>
              </div>
            </div>

            <div className="bg-card rounded-2xl p-12 text-center border border-border shadow-[0_1px_3px_rgba(0,0,0,0.04)]">
              <div className="w-20 h-20 bg-primary rounded-2xl flex items-center justify-center mx-auto mb-8">
                <Target className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-2xl font-refined text-foreground mb-6 tracking-refined">Start Your NRT Journey</h3>
              <p className="text-muted-foreground mb-8 text-base leading-relaxed max-w-2xl mx-auto font-normal">
                {loading ? (
                  <span className="inline-flex items-center gap-2">
                    Loading community data
                    <span className="inline-block bg-muted rounded w-16 h-5"></span>
                  </span>
                ) : testimonials.length > 0 ? `Join ${testimonials.length} users who have found their perfect NRT solution with detailed product comparisons, store locations, and expert reviews` :
                 'Join our growing community finding their perfect NRT solution with comprehensive product guides, price comparisons, and store locators'}
              </p>
              <Link
                to="/nrt"
                className="inline-flex items-center gap-3 bg-primary text-primary-foreground px-8 py-4 rounded-xl font-medium hover:bg-primary/90 transition-all text-lg"
                onClick={() => {
                  // Track CTA click for analytics
                  if (typeof (window as any).gtag !== 'undefined') {
                    (window as any).gtag('event', 'cta_click', {
                      event_category: 'engagement',
                      event_label: 'homepage_get_started'
                    });
                  }
                }}
              >
                <span>Browse All NRT Products</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-24 bg-background" aria-labelledby="featured-products-heading">
        <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-10">
          <div className="text-center mb-16">
            <h2 id="featured-products-heading" className="text-4xl sm:text-5xl font-royal text-foreground tracking-royal mb-6">Featured Products</h2>
            <p className="text-xl sm:text-2xl text-muted-foreground font-sophisticated leading-relaxed tracking-elegant max-w-4xl mx-auto">Explore our selection of top-rated FDA-approved nicotine replacement therapy options</p>
          </div>

          <DataErrorBoundary>
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-10">
                {Array(3).fill(null).map((_, i) => <ProductCardSkeleton key={i} />)}
              </div>
            ) : error ? (
              <div className="col-span-full text-center">
                <p className="text-destructive">Error loading products. Please try again later.</p>
              </div>
            ) : products.length === 0 ? (
              <div className="col-span-full text-center">
                <p className="text-muted-foreground">No products found.</p>
              </div>
            ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10 mt-12">
              {products.slice(0, 9).map((product) => (
                <div key={product.id} className="bg-card rounded-2xl border border-border/30 overflow-hidden shadow-[0_2px_6px_rgba(0,0,0,0.04)] hover:shadow-[0_8px_16px_rgba(0,0,0,0.08)] hover:border-border/50 transition-all duration-300 ease-in-out group h-[420px] flex flex-col">
                  {/* Product Card Top Image Container */}
                  <div className="aspect-w-16 aspect-h-9 bg-muted">
                    {product.image_url ? (
                      <img 
                        src={product.image_url} 
                        alt={product.name} 
                        className="object-cover w-full h-full" 
                        loading="lazy"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-muted/50">
                        <Package className="h-10 w-10 text-muted-foreground/70" />
                      </div>
                    )}
                  </div>
                  
                  {/* Product Card Content */}
                  <div className="p-6 flex-grow flex flex-col justify-between">
                    <div className="flex-grow">
                      <h3 className="font-refined text-foreground text-xl tracking-refined group-hover:text-primary transition-colors duration-200">{product.name}</h3>
                      <p className="text-base text-muted-foreground mt-2 font-sophisticated">{product.brand}</p>

                      {/* Rating Display */}
                      <div className="flex items-center mt-3">
                        <div className="flex text-rating-gold">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${i < (product.user_rating_avg || product.rating_avg || 0) ? 'fill-current' : ''}`}
                            />
                          ))}
                        </div>
                        <span className="ml-2 text-sm text-muted-foreground">
                          {product.user_rating_count || product.rating_count || 0} reviews
                        </span>
                      </div>

                      {/* Price and Type */}
                      <div className="mt-4 flex justify-between items-center">
                        <span className="text-foreground font-medium text-base">
                          ${product.price_range_min ? `${product.price_range_min.toFixed(2)} - ${(product.price_range_max || product.price_range_min).toFixed(2)}` : product.average_price ? product.average_price.toFixed(2) : 'Contact for Price'}
                        </span>
                        <span className="text-sm bg-primary/10 text-primary px-2 py-1 rounded-full">
                          {product.category || product.type || 'NRT'}
                        </span>
                      </div>
                    </div>

                    {/* View Details Button */}
                    <button
                      onClick={() => navigate(`/product/${product.id}`)}
                      className="mt-5 w-full bg-primary text-primary-foreground py-2.5 rounded-lg hover:bg-primary/90 transition-colors duration-200 font-medium text-sm"
                    >
                      View Details
                    </button>
                  </div>
                </div>
              ))}
            </div>
            )}
          </DataErrorBoundary>
        </div>
      </section>
      


      {/* Newsletter Signup Section - Real Database Connection */}
      <section className="bg-primary/95 py-16 backdrop-blur-md">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-2xl font-medium text-white mb-4 tracking-tight">Stay Updated on NRT Products</h2>
          <p className="text-white/90 mb-8 text-lg">Get notified about new products, research, and exclusive discounts.</p>
          <form className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto" onSubmit={handleNewsletterSignup}>
            <input
              type="email"
              name="email"
              value={newsletterEmail}
              onChange={(e) => setNewsletterEmail(e.target.value)}
              placeholder="Enter your email"
              required
              disabled={newsletterStatus === 'loading'}
              className="flex-1 px-4 py-3 rounded-lg border border-white/20 bg-white/95 backdrop-blur-sm focus:ring-2 focus:ring-white/30 focus:border-white focus:outline-none text-foreground placeholder-muted-foreground shadow-[0_1px_2px_rgba(0,0,0,0.03)] transition-all duration-200 disabled:opacity-50"
              aria-label="Email address for newsletter"
            />
            <button
              type="submit"
              disabled={newsletterStatus === 'loading'}
              className="px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-[0_1px_2px_rgba(0,0,0,0.04)] bg-white text-primary hover:bg-white/90 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {newsletterStatus === 'loading' ? 'Subscribing...' : 'Subscribe'}
            </button>
          </form>
          {newsletterMessage && (
            <p className={`text-sm mt-4 ${newsletterStatus === 'success' ? 'text-success-foreground' : newsletterStatus === 'error' ? 'text-destructive-foreground' : 'text-primary-foreground/70'}`}>
              {newsletterMessage}
            </p>
          )}
          <p className="text-white/70 text-sm mt-4">We respect your privacy. Unsubscribe at any time.</p>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-background">
        <div className="max-w-7xl mx-auto px-6">
          <h2 className="text-3xl font-semibold text-center text-foreground mb-12">What Our Users Say</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.length > 0 ? (
              testimonials.map((testimonial) => (
                <div key={testimonial.id} className="bg-muted rounded-xl p-6">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-4 h-4 ${i < testimonial.rating ? 'text-rating-gold fill-current' : 'text-muted-foreground'}`}
                      />
                    ))}
                  </div>
                  <p className="text-muted-foreground mb-4">"{testimonial.content}"</p>
                  <div className="flex items-center justify-between">
                    <p className="font-semibold text-foreground">{testimonial.user_name}</p>
                    <CheckCircle className="w-4 h-4 text-primary" />
                  </div>
                </div>
              ))
            ) : loading ? (
              // Loading state - no hardcoded data
              [...Array(3)].map((_, i) => (
                <div key={i} className="bg-muted rounded-xl p-6">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(5)].map((_, j) => (
                      <div key={j} className="w-4 h-4 bg-muted rounded"></div>
                    ))}
                  </div>
                  <div className="h-4 bg-muted rounded mb-2"></div>
                  <div className="h-4 bg-muted rounded mb-4 w-3/4"></div>
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                </div>
              ))
            ) : (
              // No testimonials available - no hardcoded fallback
              <div className="col-span-full text-center py-12">
                {loading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <TestimonialSkeleton />
                    <TestimonialSkeleton />
                    <TestimonialSkeleton />
                  </div>
                ) : (
                  <p className="text-muted-foreground font-sophisticated">No testimonials available at the moment.</p>
                )}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Apple Mac Desktop Footer */}
      <footer className="bg-background border-t border-border/30">
        <div className="max-w-7xl mx-auto px-8 py-16">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
            <div>
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-primary rounded-xl flex items-center justify-center shadow-[0_1px_3px_rgba(0,0,0,0.08)]">
                  <span className="text-white font-royal text-sm">N</span>
                </div>
                <span className="text-xl font-royal text-foreground tracking-royal">NRTList</span>
              </div>
              <p className="text-muted-foreground font-sophisticated leading-relaxed text-base">
                The premier platform for nicotine replacement therapy discovery and comparison.
              </p>
            </div>

            <div>
              <h4 className="font-refined mb-6 text-foreground tracking-refined text-base font-semibold">Products</h4>
              <ul className="space-y-3 text-muted-foreground">
                <li><Link to="/nrt" className="hover:text-foreground transition-colors duration-200 font-sophisticated text-base">Browse All</Link></li>
                <li><Link to="/nrt?category=pouches" className="hover:text-foreground transition-colors duration-200 font-sophisticated text-base">Pouches</Link></li>
                <li><Link to="/nrt?category=gum" className="hover:text-foreground transition-colors duration-200 font-sophisticated text-base">Gum</Link></li>
                <li><Link to="/nrt?category=lozenges" className="hover:text-foreground transition-colors duration-150 font-normal text-sm">Lozenges</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-refined mb-4 text-foreground tracking-refined text-sm font-semibold">Resources</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link to="/community" className="hover:text-foreground transition-colors duration-150 font-normal text-sm">Help Center</Link></li>
                <li><Link to="/community" className="hover:text-foreground transition-colors duration-150 font-normal text-sm">Community</Link></li>
                <li><Link to="/progress" className="hover:text-foreground transition-colors duration-150 font-normal text-sm">Progress Tracking</Link></li>
                <li><Link to="/reviews" className="hover:text-foreground transition-colors duration-150 font-normal text-sm">Reviews</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-refined mb-4 text-foreground tracking-refined text-sm font-semibold">Company</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link to="/about" className="hover:text-foreground transition-colors duration-150 font-normal text-sm">About</Link></li>
                <li><Link to="/privacy" className="hover:text-foreground transition-colors duration-150 font-normal text-sm">Privacy</Link></li>
                <li><Link to="/terms" className="hover:text-foreground transition-colors duration-150 font-normal text-sm">Terms</Link></li>
                <li><Link to="/contact" className="hover:text-foreground transition-colors duration-150 font-normal text-sm">Contact</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-muted mt-8 pt-6">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-muted-foreground font-normal text-sm tracking-wide opacity-80 leading-relaxed">&copy; 2024 NRTList. All rights reserved.</p>
              <div className="flex items-center gap-4 mt-3 md:mt-0">
                <a
                  href="https://twitter.com/nrtlist"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-8 h-8 rounded-lg bg-muted/30 hover:bg-muted/50 flex items-center justify-center text-muted-foreground hover:text-foreground transition-all duration-200 hover:scale-105"
                  aria-label="Follow us on Twitter"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
                <a
                  href="https://facebook.com/nrtlist"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-8 h-8 rounded-lg bg-muted/30 hover:bg-muted/50 flex items-center justify-center text-muted-foreground hover:text-foreground transition-all duration-200 hover:scale-105"
                  aria-label="Follow us on Facebook"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M20 10C20 4.477 15.523 0 10 0S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z" clipRule="evenodd" />
                  </svg>
                </a>
                <a
                  href="https://instagram.com/nrtlist"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-8 h-8 rounded-lg bg-muted/30 hover:bg-muted/50 flex items-center justify-center text-muted-foreground hover:text-foreground transition-all duration-200 hover:scale-105"
                  aria-label="Follow us on Instagram"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 0C4.477 0 0 4.477 0 10s4.477 10 10 10 10-4.477 10-10S15.523 0 10 0zm4.5 6.5h-9v7h9v-7z" clipRule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>

      {/* Auth Modal */}
      {showAuthModal && (
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          mode={authMode}
        />
      )}
    </div>
  );
};

export default LandingPage;
