import React, { useState, useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { Package, Star, Award, Users, TrendingUp, Search, Grid, List, Filter, Sparkles, Shield, AlertTriangle } from 'lucide-react';
import { getNRTProducts, supabase } from '../lib/supabase';
import Rating from '../components/Rating';

interface Product {
  id: string;
  name: string;
  brand: string;
  category: string;
  description: string;
  image_url: string;
  user_rating_avg: number | null;
  user_rating_count: number | null;
  nicotine_strengths: string[] | null;
  flavors: string[] | null;
  is_verified: boolean | null;
  created_at: string;
  updated_at: string;
  is_fda_approved?: boolean;
  product_type?: 'nrt' | 'smokeless';
  tags?: string[];
}

interface BrandStats {
  name: string;
  productCount: number;
  avgRating: number;
  totalReviews: number;
  topRatedCount: number;
  verifiedCount: number;
  categories: string[];
  newestProduct: string;
  fdaApprovedCount: number;
  smokelessCount: number;
  complianceType: 'fda-only' | 'smokeless-only' | 'mixed';
}

const BrandsPage: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('products');
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filters, setFilters] = useState({
    minRating: 0,
    minProducts: 0,
    verified: false,
    category: 'all'
  });

  // Load products from database - HOLY RULE #1 COMPLIANT
  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoading(true);
        const productData = await getNRTProducts();
        // Ensure compatibility with Product interface
        const formattedData = productData.map((product: any) => ({
          ...product,
          is_verified: product.is_verified || false,
          updated_at: product.updated_at || product.created_at
        }));
        setProducts(formattedData);
        setError(null);
      } catch (error) {
        console.error('Error loading products:', error);
        setError('Failed to load brands data');
      } finally {
        setLoading(false);
      }
    };

    loadProducts();
  }, []);

  // Calculate brand stats from real database data
  const brandStats: BrandStats[] = useMemo(() => {
    if (!products.length) return [];

    const brandMap = new Map<string, BrandStats>();

    products.forEach(product => {
      const brandName = product.brand;

      if (!brandMap.has(brandName)) {
        brandMap.set(brandName, {
          name: brandName,
          productCount: 0,
          avgRating: 0,
          totalReviews: 0,
          topRatedCount: 0,
          verifiedCount: 0,
          categories: [],
          newestProduct: product.created_at,
          fdaApprovedCount: 0,
          smokelessCount: 0,
          complianceType: 'mixed'
        });
      }

      const brand = brandMap.get(brandName)!;
      brand.productCount++;
      brand.avgRating += (product.user_rating_avg || 0);
      brand.totalReviews += (product.user_rating_count || 0);

      if ((product.user_rating_avg || 0) >= 4.5) {
        brand.topRatedCount++;
      }

      if (product.is_verified) {
        brand.verifiedCount++;
      }

      // Track legal compliance
      if (product.is_fda_approved) {
        brand.fdaApprovedCount++;
      } else {
        brand.smokelessCount++;
      }

      if (!brand.categories.includes(product.category)) {
        brand.categories.push(product.category);
      }

      if (new Date(product.created_at) > new Date(brand.newestProduct)) {
        brand.newestProduct = product.created_at;
      }
    });
    
    // Calculate average ratings and compliance types
    brandMap.forEach(brand => {
      brand.avgRating = brand.productCount > 0 ? brand.avgRating / brand.productCount : 0;

      // Determine compliance type
      if (brand.fdaApprovedCount > 0 && brand.smokelessCount === 0) {
        brand.complianceType = 'fda-only';
      } else if (brand.smokelessCount > 0 && brand.fdaApprovedCount === 0) {
        brand.complianceType = 'smokeless-only';
      } else {
        brand.complianceType = 'mixed';
      }
    });

    return Array.from(brandMap.values());
  }, [products]);

  // Simplified filtered brands for testing
  const filteredBrands: BrandStats[] = [];

  // Simplified overall stats for testing
  const overallStats = {
    totalBrands: 0,
    totalProducts: 0,
    avgRating: 0,
    totalReviews: 0,
    topBrands: 0,
    categories: []
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-20 h-20 bg-wellness rounded-3xl flex items-center justify-center animate-pulse mx-auto mb-6">
            <Package className="w-10 h-10 text-primary-foreground" />
          </div>
          <p className="text-wellness font-sophisticated text-lg">Loading brand directory...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-20 h-20 bg-alert-red rounded-3xl flex items-center justify-center mx-auto mb-6">
            <Package className="w-10 h-10 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-wellness mb-4">Error Loading Brands</h2>
          <p className="text-wellness mb-6">{error}</p>
          <Link
            to="/products"
            className="bg-wellness text-white px-6 py-3 rounded-2xl hover:bg-wellness-hover transition-colors font-medium"
          >
            Browse Products Instead
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Apple-style Hero Section */}
      <div className="bg-gradient-to-br from-white via-gray-50 to-gray-100">
        <div className="max-w-7xl mx-auto px-6 py-16">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-3 bg-muted/20 px-6 py-3 rounded-full mb-6">
              <Sparkles className="w-5 h-5 text-wellness" />
              <span className="text-wellness font-sophisticated">Brand Directory</span>
            </div>
            <h1 className="text-3xl font-royal text-foreground mb-6 tracking-royal">
              NRT Brands
            </h1>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto leading-relaxed font-sophisticated">
              Discover trusted brands in nicotine replacement therapy. Compare products,
              ratings, and reviews from leading manufacturers worldwide.
            </p>
          </div>

          {/* Brand Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-wellness/50 text-center">
              <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Package className="w-8 h-8 text-wellness" />
              </div>
              <div className="text-3xl font-bold text-wellness mb-2">
                {overallStats.totalBrands}
              </div>
              <div className="text-wellness font-medium">Total Brands</div>
            </div>
            
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-wellness/50 text-center">
              <div className="w-16 h-16 bg-rating-gold-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-rating-gold" />
              </div>
              <div className="text-3xl font-bold text-wellness mb-2">
                {overallStats.avgRating.toFixed(1)}
              </div>
              <div className="text-wellness font-medium">Average Rating</div>
            </div>
            
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-wellness/50 text-center">
              <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-wellness" />
              </div>
              <div className="text-3xl font-bold text-wellness mb-2">
                {overallStats.totalReviews}
              </div>
              <div className="text-wellness font-medium">Total Reviews</div>
            </div>
            
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-wellness/50 text-center">
              <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Award className="w-8 h-8 text-wellness" />
              </div>
              <div className="text-3xl font-bold text-wellness mb-2">
                {overallStats.topBrands}
              </div>
              <div className="text-wellness font-medium">Top Rated</div>
            </div>
          </div>
        </div>
      </div>

      {/* Apple-style Filters and Controls */}
      <div className="bg-white border-b border-wellness sticky top-16 z-40">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            {/* Search Bar */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-wellness w-5 h-5" />
              <input
                type="text"
                name="brandSearch"
                placeholder="Search brands..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-3 border border-wellness rounded-2xl focus:outline-none focus:ring-2 focus:ring-wellness focus:border-transparent bg-white hover:bg-white transition-colors"
              />
            </div>

            {/* Controls */}
            <div className="flex items-center gap-4">
              {/* View Mode Toggle */}
              <div className="flex items-center bg-white rounded-2xl p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-xl transition-colors ${
                    viewMode === 'grid'
                      ? 'bg-white text-wellness shadow-sm'
                      : 'text-wellness hover:text-wellness'
                  }`}
                >
                  <Grid className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-xl transition-colors ${
                    viewMode === 'list'
                      ? 'bg-white text-wellness shadow-sm'
                      : 'text-wellness hover:text-wellness'
                  }`}
                >
                  <List className="w-5 h-5" />
                </button>
              </div>

              {/* Sort Dropdown */}
              <select
                name="sortBy"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-3 border border-wellness rounded-2xl focus:outline-none focus:ring-2 focus:ring-wellness focus:border-transparent bg-white font-medium text-wellness"
              >
                <option value="products">Most Products</option>
                <option value="rating">Highest Rated</option>
                <option value="name">Name A-Z</option>
                <option value="reviews">Most Reviews</option>
                <option value="newest">Newest Products</option>
              </select>

              {/* Filters Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center gap-2 px-4 py-3 rounded-2xl border transition-colors font-medium ${
                  showFilters
                    ? 'bg-wellness text-white border-wellness'
                    : 'bg-white text-wellness border-wellness hover:border-wellness'
                }`}
              >
                <Filter className="w-5 h-5" />
                Filters
              </button>
            </div>
          </div>

          {/* Advanced Filters Panel */}
          {showFilters && (
            <div className="mt-6 p-6 bg-white rounded-2xl border border-wellness">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                {/* Rating Filter */}
                <div>
                  <label className="block text-sm font-medium text-wellness mb-2">
                    Minimum Rating
                  </label>
                  <select
                    name="minRating"
                    value={filters.minRating}
                    onChange={(e) => setFilters({...filters, minRating: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-wellness rounded-xl focus:ring-2 focus:ring-wellness focus:border-transparent"
                  >
                    <option value={0}>Any Rating</option>
                    <option value={4}>4+ Stars</option>
                    <option value={4.5}>4.5+ Stars</option>
                  </select>
                </div>

                {/* Products Filter */}
                <div>
                  <label className="block text-sm font-medium text-wellness mb-2">
                    Minimum Products
                  </label>
                  <select
                    name="minProducts"
                    value={filters.minProducts}
                    onChange={(e) => setFilters({...filters, minProducts: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-wellness rounded-xl focus:ring-2 focus:ring-wellness focus:border-transparent"
                  >
                    <option value={0}>Any Amount</option>
                    <option value={5}>5+ Products</option>
                    <option value={10}>10+ Products</option>
                  </select>
                </div>

                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-medium text-wellness mb-2">
                    Category
                  </label>
                  <select
                    name="category"
                    value={filters.category}
                    onChange={(e) => setFilters({...filters, category: e.target.value})}
                    className="w-full px-3 py-2 border border-wellness rounded-xl focus:ring-2 focus:ring-wellness focus:border-transparent"
                  >
                    <option value="all">All Categories</option>
                    {overallStats.categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                {/* Clear Filters */}
                <div className="flex items-end">
                  <button
                    onClick={() => setFilters({
                      minRating: 0,
                      minProducts: 0,
                      verified: false,
                      category: 'all'
                    })}
                    className="w-full px-4 py-2 text-sm font-medium text-wellness hover:text-wellness transition-colors"
                  >
                    Clear All Filters
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Apple-style Brand Grid */}
      <div className="max-w-7xl mx-auto px-6 py-16">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold text-wellness">
            All Brands
            <span className="ml-3 text-wellness font-normal">({filteredBrands.length})</span>
          </h2>
        </div>

        {filteredBrands.length > 0 ? (
          <div className={viewMode === 'grid'
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
            : 'space-y-6'
          }>
            {filteredBrands.map(brand => (
              <Link
                key={brand.name}
                to={`/brand/${encodeURIComponent(brand.name.toLowerCase().replace(/\s+/g, '-'))}`}
                className={`group bg-white rounded-3xl shadow-lg border border-wellness/50 hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 overflow-hidden ${
                  viewMode === 'list' ? 'flex items-center p-8' : 'p-8'
                }`}
              >
                {viewMode === 'grid' ? (
                  <div>
                    <div className="flex items-start justify-between mb-6">
                      <div className="flex-1">
                        <h3 className="font-bold text-wellness text-xl mb-2 group-hover:text-wellness transition-colors">
                          {brand.name}
                        </h3>
                        <p className="text-wellness text-sm mb-4">
                          {brand.productCount} products across {brand.categories.length} categories
                        </p>
                        <div className="flex items-center gap-2 mb-4">
                          <Rating
                            rating={brand.avgRating}
                            size="sm"
                            showNumber={true}
                          />
                          <span className="text-sm text-wellness">
                            ({brand.totalReviews} reviews)
                          </span>
                        </div>

                        {/* Legal Compliance Indicator */}
                        <div className="mb-4">
                          {brand.complianceType === 'fda-only' ? (
                            <div className="flex items-center gap-2 px-3 py-1 bg-primary/10 border border-primary/20 rounded-lg">
                              <Shield className="w-4 h-4 text-primary" />
                              <span className="text-sm font-sophisticated text-primary">FDA-Approved Products Only</span>
                            </div>
                          ) : brand.complianceType === 'smokeless-only' ? (
                            <div className="flex items-center gap-2 px-3 py-1 bg-primary/10 border border-primary/20 rounded-lg">
                              <AlertTriangle className="w-4 h-4 text-primary" />
                              <span className="text-sm font-sophisticated text-primary">Non-FDA Smokeless Products Only</span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2 px-3 py-1 bg-primary/10 border border-primary/20 rounded-lg">
                              <AlertTriangle className="w-4 h-4 text-primary" />
                              <span className="text-sm font-medium text-primary">Mixed: FDA & Non-FDA Products</span>
                            </div>
                          )}
                        </div>
                      </div>
                      {brand.verifiedCount > 0 && (
                        <div className="ml-4 bg-wellness-50 p-2 rounded-xl">
                          <Award className="w-5 h-5 text-wellness" />
                        </div>
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div className="bg-primary/10 rounded-2xl p-4">
                        <div className="text-2xl font-bold text-primary mb-1">
                          {brand.fdaApprovedCount}
                        </div>
                        <div className="text-sm text-primary">FDA Products</div>
                      </div>
                      <div className="bg-primary/10 rounded-2xl p-4">
                        <div className="text-2xl font-bold text-primary mb-1">
                          {brand.smokelessCount}
                        </div>
                        <div className="text-sm text-primary">Non-FDA Products</div>
                      </div>
                    </div>

                    <div className="mt-6">
                      <div className="text-sm text-wellness mb-2">Categories:</div>
                      <div className="flex flex-wrap gap-2">
                        {brand.categories.slice(0, 3).map(category => (
                          <span
                            key={category}
                            className="px-3 py-1 bg-wellness-50 text-wellness text-xs font-medium rounded-full"
                          >
                            {category}
                          </span>
                        ))}
                        {brand.categories.length > 3 && (
                          <span className="px-3 py-1 bg-white text-wellness text-xs font-medium rounded-full">
                            +{brand.categories.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center w-full">
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="font-bold text-wellness text-xl mb-1 group-hover:text-wellness transition-colors">
                            {brand.name}
                          </h3>
                          <p className="text-wellness mb-2">
                            {brand.productCount} products • {brand.categories.length} categories
                          </p>
                          <div className="flex items-center gap-2 mb-2">
                            <Rating
                              rating={brand.avgRating}
                              size="sm"
                              showNumber={true}
                            />
                            <span className="text-sm text-wellness">
                              ({brand.totalReviews} reviews)
                            </span>
                          </div>

                          {/* Legal Compliance Indicator for List View */}
                          <div className="mb-2">
                            {brand.complianceType === 'fda-only' ? (
                              <div className="flex items-center gap-2 px-2 py-1 bg-primary/10 border border-primary/20 rounded text-xs">
                                <Shield className="w-3 h-3 text-primary" />
                                <span className="font-sophisticated text-primary">FDA-Approved Only</span>
                              </div>
                            ) : brand.complianceType === 'smokeless-only' ? (
                              <div className="flex items-center gap-2 px-2 py-1 bg-primary/10 border border-primary/20 rounded text-xs">
                                <AlertTriangle className="w-3 h-3 text-primary" />
                                <span className="font-sophisticated text-primary">Non-FDA Only</span>
                              </div>
                            ) : (
                              <div className="flex items-center gap-2 px-2 py-1 bg-primary/10 border border-primary/20 rounded text-xs">
                                <AlertTriangle className="w-3 h-3 text-primary" />
                                <span className="font-medium text-primary">Mixed Products</span>
                              </div>
                            )}
                          </div>
                        </div>
                        {brand.verifiedCount > 0 && (
                          <div className="bg-wellness-50 p-2 rounded-xl">
                            <Award className="w-5 h-5 text-wellness" />
                          </div>
                        )}
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex flex-wrap gap-2">
                          {brand.categories.slice(0, 4).map(category => (
                            <span
                              key={category}
                              className="px-3 py-1 bg-wellness-50 text-wellness text-xs font-medium rounded-full"
                            >
                              {category}
                            </span>
                          ))}
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-wellness">Top Rated</div>
                          <div className="font-semibold text-wellness">
                            {brand.topRatedCount} products
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-20">
            <div className="w-32 h-32 bg-white rounded-3xl flex items-center justify-center mx-auto mb-8">
              <Package className="w-16 h-16 text-wellness" />
            </div>
            <h3 className="text-2xl font-bold text-wellness mb-4">
              No Brands Found
            </h3>
            <p className="text-wellness mb-8 max-w-md mx-auto">
              {searchQuery || Object.values(filters).some(f => f !== 'all' && f !== false && f !== 0)
                ? 'Try adjusting your search or filters to find more brands.'
                : 'No brands are available at the moment. Check back soon for updates.'
              }
            </p>
            <div className="flex gap-4 justify-center">
              {(searchQuery || Object.values(filters).some(f => f !== 'all' && f !== false && f !== 0)) && (
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setFilters({
                      minRating: 0,
                      minProducts: 0,
                      verified: false,
                      category: 'all'
                    });
                  }}
                  className="bg-wellness text-white px-6 py-3 rounded-2xl hover:bg-wellness-hover transition-colors font-medium"
                >
                  Clear Filters
                </button>
              )}
              <Link
                to="/products"
                className="bg-white text-wellness px-6 py-3 rounded-2xl hover:bg-gray-200 transition-colors font-medium"
              >
                Browse All Products
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BrandsPage;
