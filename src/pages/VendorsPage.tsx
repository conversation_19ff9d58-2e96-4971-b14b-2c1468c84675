import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Package, Sparkles, TrendingUp, Shield } from 'lucide-react';
import { supabase } from '../lib/supabase';


const VendorsPage: React.FC = () => {
  const [vendorStats, setVendorStats] = useState({
    totalVendors: 0,
    avgRating: 0,
    verifiedCount: 0
  });

  useEffect(() => {
    const loadVendorStats = async () => {
      try {
        const { data: vendors, error } = await supabase
          .schema('mission_fresh')
          .from('vendors')
          .select('rating, verified');

        if (error) {
          console.error('Error loading vendor stats:', error);
          // Show zero stats if query fails - no hardcoded data (HOLY RULE #1)
          setVendorStats({
            totalVendors: 0,
            avgRating: 0,
            verifiedCount: 0
          });
          return;
        }

        const totalVendors = vendors?.length || 0;
        const avgRating = totalVendors > 0
          ? vendors.reduce((sum, vendor) => sum + (vendor.rating || 0), 0) / totalVendors
          : 0;
        const verifiedCount = vendors?.filter(vendor => vendor.verified).length || 0;

        setVendorStats({
          totalVendors,
          avgRating,
          verifiedCount
        });
      } catch (error) {
        console.error('Error loading vendor stats:', error);
      }
    };

    loadVendorStats();
  }, []);

  return (
    <div className="min-h-screen bg-white">


      {/* Breadcrumb Navigation */}
      <div className="bg-white border-b border-wellness">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center space-x-2 text-sm">
            <Link to="/" className="text-wellness hover:text-wellness transition-colors font-medium">
              Home
            </Link>
            <span className="text-wellness">/</span>
            <span className="text-wellness font-medium">Vendors</span>
          </div>
        </div>
      </div>

      {/* Apple-style Hero Section */}
      <div className="bg-gradient-to-br from-white via-wellness-50 to-white">
        <div className="max-w-7xl mx-auto px-6 py-16">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-3 bg-wellness-50 px-6 py-3 rounded-full mb-6">
              <Sparkles className="w-5 h-5 text-wellness" />
              <span className="text-wellness font-semibold">Online Vendors</span>
            </div>
            <h1 className="text-3xl font-royal text-foreground mb-6 tracking-royal">
              NRT Vendors
            </h1>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto leading-relaxed font-sophisticated">
              Compare prices, shipping options, and reviews from trusted online vendors.
              Find the best deals on NRT products with fast, reliable delivery.
            </p>
          </div>

          {/* Vendor Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="bg-background/98 backdrop-blur-md rounded-2xl p-6 shadow-[0_4px_16px_rgba(0,0,0,0.06)] border border-border/20 text-center hover:border-border/40 hover:shadow-[0_8px_24px_rgba(0,0,0,0.08)] transition-all duration-300 group">
              <div className="w-12 h-12 bg-gradient-to-br from-primary/15 to-primary/5 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-[0_3px_8px_rgba(34,197,94,0.12)] border border-primary/10 group-hover:scale-105 transition-transform duration-300">
                <Package className="w-6 h-6 text-primary" />
              </div>
              <div className="text-2xl font-royal text-foreground mb-2 tracking-royal">{vendorStats.totalVendors}</div>
              <div className="text-sm text-muted-foreground font-sophisticated tracking-elegant">Total Vendors</div>
            </div>

            <div className="bg-background/98 backdrop-blur-md rounded-2xl p-6 shadow-[0_4px_16px_rgba(0,0,0,0.06)] border border-border/20 text-center hover:border-border/40 hover:shadow-[0_8px_24px_rgba(0,0,0,0.08)] transition-all duration-300 group">
              <div className="w-12 h-12 bg-gradient-to-br from-primary/15 to-primary/5 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-[0_3px_8px_rgba(34,197,94,0.12)] border border-primary/10 group-hover:scale-105 transition-transform duration-300">
                <TrendingUp className="w-6 h-6 text-primary" />
              </div>
              <div className="text-2xl font-royal text-foreground mb-2 tracking-royal">{vendorStats.avgRating.toFixed(1)}</div>
              <div className="text-sm text-muted-foreground font-sophisticated tracking-elegant">Average Rating</div>
            </div>

            <div className="bg-background/98 backdrop-blur-md rounded-2xl p-6 shadow-[0_4px_16px_rgba(0,0,0,0.06)] border border-border/20 text-center hover:border-border/40 hover:shadow-[0_8px_24px_rgba(0,0,0,0.08)] transition-all duration-300 group">
              <div className="w-12 h-12 bg-gradient-to-br from-primary/15 to-primary/5 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-[0_3px_8px_rgba(34,197,94,0.12)] border border-primary/10 group-hover:scale-105 transition-transform duration-300">
                <Shield className="w-6 h-6 text-primary" />
              </div>
              <div className="text-2xl font-royal text-foreground mb-2 tracking-royal">{vendorStats.verifiedCount}</div>
              <div className="text-sm text-muted-foreground font-sophisticated tracking-elegant">Verified Vendors</div>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
};

export default VendorsPage;
