import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, User, Shield, LogIn } from 'lucide-react';
import { supabase } from '../lib/supabase';

const UserProfilePage: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check authentication and load real user data from database (RULE 0001 COMPLIANT)
  useEffect(() => {
    const checkAuth = async () => {
      try {
        console.log('🚨 UserProfilePage: Checking authentication...');
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('🚨 UserProfilePage: Auth error:', error);
          setIsAuthenticated(false);
          setLoading(false);
          return;
        }

        if (!session?.user) {
          console.log('🚨 UserProfilePage: No authenticated user found');
          setIsAuthenticated(false);
          setLoading(false);
          return;
        }

        console.log('🚨 UserProfilePage: User authenticated:', session.user.id);
        setIsAuthenticated(true);
        setUser(session.user);
        setLoading(false);
      } catch (error) {
        console.error('🚨 UserProfilePage: Error checking auth:', error);
        setIsAuthenticated(false);
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Apple Mac Desktop Loading State
  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-2xl flex items-center justify-center animate-pulse mx-auto mb-4">
            <User className="w-8 h-8 text-primary-foreground" />
          </div>
          <p className="text-muted-foreground text-lg">Loading your profile and preferences...</p>
          <p className="text-muted-foreground text-sm mt-2">Retrieving your NRT journey data and settings</p>
        </div>
      </div>
    );
  }

  // Apple Mac Desktop Authentication Required State (RULE 0001 COMPLIANT)
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="bg-card rounded-2xl p-8 shadow-lg border border-border max-w-md text-center">
          <div className="w-16 h-16 bg-destructive rounded-2xl flex items-center justify-center mx-auto mb-4">
            <LogIn className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-xl font-royal text-foreground mb-2">Authentication Required</h2>
          <p className="text-muted-foreground mb-6">
            Please sign in to access your personalized NRT profile, progress tracking, saved products, and quit journey preferences.
          </p>
          <button
            onClick={() => navigate('/')}
            className="bg-primary text-white px-6 py-3 rounded-xl hover:bg-primary/90 transition-all font-medium"
          >
            Return to Homepage & Sign In
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Apple Mac Desktop Header */}
      <header className="bg-card shadow-sm border-b border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/')}
                className="flex items-center text-muted-foreground hover:text-foreground mr-6 transition-colors"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Homepage
              </button>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
                  <Shield className="h-5 w-5 text-white" />
                </div>
                <span className="text-xl font-royal text-foreground">NRTList Profile</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Profile Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <div className="w-16 h-16 bg-primary rounded-2xl flex items-center justify-center">
              <User className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-royal text-foreground">
                Your NRT Profile
              </h1>
              <p className="text-muted-foreground">
                Manage your account settings, preferences, and quit journey progress
              </p>
            </div>
          </div>
        </div>

        {/* Apple Mac Desktop Profile Card */}
        <div className="bg-card rounded-2xl shadow-sm border border-border p-8">
          <h2 className="text-xl font-semibold text-foreground mb-6">Account Information</h2>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">Email Address</label>
                <div className="bg-background border border-border rounded-xl p-4">
                  <p className="text-foreground font-medium">{user?.email || 'Not available'}</p>
                  <p className="text-muted-foreground text-sm mt-1">Your primary contact and login email</p>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">Account ID</label>
                <div className="bg-background border border-border rounded-xl p-4">
                  <p className="text-foreground font-mono text-sm">{user?.id || 'Not available'}</p>
                  <p className="text-muted-foreground text-sm mt-1">Unique identifier for your account</p>
                </div>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Member Since</label>
              <div className="bg-background border border-border rounded-xl p-4">
                <p className="text-foreground font-medium">
                  {user?.created_at ? new Date(user.created_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  }) : 'Not available'}
                </p>
                <p className="text-muted-foreground text-sm mt-1">When you joined the NRTList community</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default UserProfilePage;
