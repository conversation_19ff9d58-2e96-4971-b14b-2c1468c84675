import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom';
import { ArrowLeft, Star, Heart, Share2, Shield, Tag, Users } from 'lucide-react';

import { getProductById } from '../lib/supabase';
import { supabase } from '../lib/supabase';
import ReviewSystem from '../components/ReviewSystem';
import VendorList from '../components/VendorList';
import StoreLocator from '../components/StoreLocator';
import PriceComparisonSystem from '../components/PriceComparisonSystem';
import DealsSystem from '../components/DealsSystem';
import SocialSystem from '../components/SocialSystem';

interface Product {
  id: string;
  name: string;
  description: string;
  image_url?: string;
  brand: string;
  category: string;
  nicotine_strengths?: any; // jsonb field from database
  flavors?: string[];
  user_rating_avg?: number;
  user_rating_count?: number;
  is_verified: boolean;
  tags?: string[];
  created_at: string;
  updated_at: string;
}

interface Review {
  id: string;
  user_id: string;
  username: string;
  rating: number;
  review: string;
  created_at: string;
  helpful_count: number;
  verified_purchase: boolean;
  parameters?: {
    effectiveness: number;
    sideEffects: number;
    easeOfUse: number;
    taste: number;
    adherence: number;
    value: number;
    nicotineDelivery: number;
  };
}

const ProductDetailPage: React.FC = () => {
  const { id: productId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [product, setProduct] = useState<Product | null>(null);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [isFavorite, setIsFavorite] = useState(false);

  useEffect(() => {
    const loadProduct = async () => {
      if (!productId) {
        setError('Product ID is required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const productData = await getProductById(productId);

        if (!productData) {
          setError('Product not found');
          return;
        }

        setProduct(productData);

        // Load reviews from appropriate table based on product type
        const isFDAApproved = productData.tags?.includes('FDA-approved') || productData.tags?.includes('NRT');
        const reviewTable = isFDAApproved ? 'nrt_product_reviews' : 'smokeless_product_reviews';

        const { data: reviewsData, error: reviewsError } = await supabase
          .from(reviewTable)
          .select('*')
          .eq('product_id', productId)
          .order('created_at', { ascending: false });

        if (reviewsError) {
          console.log(`No reviews found in ${reviewTable} for product ${productId}`);
          setReviews([]);
        } else {
          setReviews(reviewsData || []);
        }

        // Check if user has favorited this product
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          const { data: favoriteData } = await supabase
            .from('user_favorites')
            .select('id')
            .eq('user_id', user.id)
            .eq('product_id', productId)
            .single();

          setIsFavorite(!!favoriteData);
        }
        
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load product');
      } finally {
        setLoading(false);
      }
    };

    loadProduct();
  }, [productId]);

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product?.name,
          text: product?.description,
          url: window.location.href,
        });
      } catch (err) {
        // Silently handle sharing errors
      }
    } else {
      // Fallback: copy to clipboard
      await navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  const toggleFavorite = async () => {
    if (!product) return;

    try {
      setIsFavorite(!isFavorite);

      // Save to database - check if user is logged in first
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        // If not logged in, just keep local state
        return;
      }

      if (!isFavorite) {
        // Add to favorites
        await supabase
          .from('user_favorites')
          .insert({
            user_id: user.id,
            product_id: product.id,
            created_at: new Date().toISOString()
          });
      } else {
        // Remove from favorites
        await supabase
          .from('user_favorites')
          .delete()
          .eq('user_id', user.id)
          .eq('product_id', product.id);
      }
    } catch (error) {
      // Revert state on error
      setIsFavorite(isFavorite);
    }
  };

  const ratingParameters = [
    { key: 'effectiveness', label: 'Effectiveness', description: 'How well does it reduce cravings?' },
    { key: 'sideEffects', label: 'Side Effects', description: 'Minimal side effects?' },
    { key: 'easeOfUse', label: 'Ease of Use', description: 'How easy is it to use?' },
    { key: 'taste', label: 'Taste/Flavor', description: 'How does it taste?' },
    { key: 'adherence', label: 'Adherence', description: 'Easy to stick with?' },
    { key: 'value', label: 'Value', description: 'Good value for money?' },
    { key: 'nicotineDelivery', label: 'Nicotine Delivery', description: 'Consistent nicotine delivery?' }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="flex flex-col justify-center items-center min-h-[400px]">
            <div className="w-16 h-16 bg-primary rounded-2xl flex items-center justify-center animate-pulse">
              <Shield className="w-8 h-8 text-white" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-background">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-primary mb-4">Product Not Found</h1>
            <p className="text-primary mb-4">
              {error && typeof error === 'string' ? error : 'The product you\'re looking for doesn\'t exist.'}
            </p>
            <button
              onClick={() => navigate('/')}
              className="bg-primary text-white px-4 py-2 rounded-2xl hover:bg-primary/90 transition-colors"
            >
              Go Back Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Product loaded with real NRT data structure - no safety check needed

  return (
    <div className="min-h-screen bg-background">
      {/* Modern Enhanced Header with Reasonable Font Sizes */}
      <header className="bg-background/98 backdrop-blur-md border-b border-border/30 shadow-[0_1px_3px_rgba(0,0,0,0.04)]">
        <div className="max-w-7xl mx-auto px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-6">
              <button
                onClick={() => navigate('/')}
                className="flex items-center text-muted-foreground hover:text-foreground transition-colors duration-300 text-sm font-sophisticated group"
              >
                <ArrowLeft className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
                Back to Products
              </button>
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-primary rounded-xl flex items-center justify-center shadow-[0_2px_6px_rgba(16,185,129,0.15)]">
                  <Shield className="h-4 w-4 text-white" />
                </div>
                <span className="text-lg font-royal text-foreground tracking-royal">NRTList</span>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={toggleFavorite}
                className={`p-3 rounded-xl transition-all duration-300 ${
                  isFavorite
                    ? 'text-primary bg-primary/10 shadow-[0_2px_8px_rgba(16,185,129,0.15)]'
                    : 'text-muted-foreground hover:text-foreground hover:bg-background/80 hover:shadow-[0_2px_6px_rgba(0,0,0,0.06)]'
                }`}
              >
                <Heart className={`h-5 w-5 ${isFavorite ? 'fill-current' : ''} transition-transform duration-300`} />
              </button>
              <button
                onClick={handleShare}
                className="p-3 rounded-xl text-muted-foreground hover:text-foreground hover:bg-background/80 hover:shadow-[0_2px_6px_rgba(0,0,0,0.06)] transition-all duration-300"
              >
                <Share2 className="h-5 w-5 transition-transform duration-300 hover:scale-110" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Enhanced Product Header with Reasonable Font Sizes */}
      <div className="bg-background/98 backdrop-blur-sm border-b border-border/30">
        <div className="max-w-7xl mx-auto px-8 py-8">
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <div className="flex items-center gap-3 mb-4">
                <span className="bg-primary/10 text-primary px-3 py-1.5 rounded-xl text-sm font-medium border border-primary/20">
                  {product.category}
                </span>
                <span className="bg-muted/50 text-muted-foreground px-3 py-1.5 rounded-xl text-sm font-medium border border-border/30">
                  {product.brand}
                </span>
              </div>
              <h1 className="text-2xl md:text-3xl font-royal text-foreground mb-4 leading-tight tracking-royal">{product.name}</h1>
              <p className="text-muted-foreground mb-6 font-sophisticated text-base leading-relaxed">{product.description}</p>

              {/* Legal Compliance Warning */}
              {(() => {
                const isFDAApproved = product.tags?.includes('FDA-approved') || product.tags?.includes('NRT');
                if (isFDAApproved) {
                  return (
                    <div className="bg-primary/10 border border-primary/30 rounded-lg p-4 mb-4">
                      <div className="flex items-center gap-2">
                        <Shield className="w-5 h-5 text-primary" />
                        <span className="font-semibold text-primary">FDA-Approved NRT Product</span>
                      </div>
                      <p className="text-primary/80 text-sm mt-1">
                        This product is FDA-approved as a nicotine replacement therapy for smoking cessation.
                      </p>
                    </div>
                  );
                } else {
                  return (
                    <div className="bg-alert-red/10 border border-alert-red/30 rounded-lg p-4 mb-4">
                      <div className="flex items-center gap-2">
                        <Tag className="w-5 h-5 text-alert-red" />
                        <span className="font-semibold text-alert-red">Not FDA-Approved as NRT</span>
                      </div>
                      <p className="text-alert-red text-opacity-80 text-sm mt-1">
                        This product is NOT FDA-approved as nicotine replacement therapy. It is a smokeless alternative.
                        You must be 21+ to purchase. Consult healthcare provider for smoking cessation.
                      </p>
                    </div>
                  );
                }
              })()}

              <div className="flex items-center gap-4 mb-6">
                <div className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-primary fill-primary" strokeWidth={1} />
                  <span className="text-lg font-royal text-foreground">{product.user_rating_avg || 0}</span>
                  <span className="text-muted-foreground text-sm font-sophisticated">({product.user_rating_count || 0} reviews)</span>
                </div>
                <div className="text-base font-sophisticated text-foreground">
                  {/* Pricing from database only */}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3 mb-4">
                <div>
                  <span className="text-xs text-muted-foreground font-normal">Strength</span>
                  <p className="font-normal text-sm text-foreground">
                    {(() => {
                      if (!product.nicotine_strengths) return '';

                      if (Array.isArray(product.nicotine_strengths)) {
                        return product.nicotine_strengths
                          .map(s => {
                            if (typeof s === 'object' && s.value && s.unit) {
                              return `${s.value}${s.unit}`;
                            }
                            return s ? s.toString() : '';
                          })
                          .filter(s => s)
                          .join(', ') || '';
                      }

                      if (typeof product.nicotine_strengths === 'object') {
                        if (product.nicotine_strengths.value && product.nicotine_strengths.unit) {
                          return `${product.nicotine_strengths.value}${product.nicotine_strengths.unit}`;
                        }
                        const values = Object.values(product.nicotine_strengths)
                          .filter(v => v && typeof v !== 'object')
                          .map(v => (v as string | number).toString())
                          .filter(v => v && v !== 'null' && v !== 'undefined');
                        return values.length > 0 ? values.join(', ') : '';
                      }

                      return product.nicotine_strengths.toString();
                    })()}
                  </p>
                </div>
                <div>
                  <span className="text-xs text-muted-foreground font-normal">Flavor</span>
                  <p className="font-normal text-sm text-foreground">{product.flavors?.join(', ') || ''}</p>
                </div>
              </div>
            </div>

            <div>
              <div className="bg-muted/30 rounded-lg p-4 h-64 flex items-center justify-center overflow-hidden">
                {product.image_url ? (
                  <img
                    src={product.image_url}
                    alt={product.name}
                    className="w-full h-full object-cover rounded-md"
                    onError={(e) => {
                      // Hide broken image and show fallback
                      e.currentTarget.style.display = 'none';
                      const fallback = e.currentTarget.nextElementSibling as HTMLElement;
                      if (fallback) fallback.style.display = 'flex';
                    }}
                  />
                ) : null}
                {/* Fallback for broken or missing images */}
                <div
                  className={`w-full h-full items-center justify-center ${product.image_url ? 'hidden' : 'flex'}`}
                >
                  <div className="text-center">
                    <Shield className="h-12 w-12 text-primary mx-auto mb-2" />
                    <p className="text-foreground font-semibold text-base mb-1">{product.brand}</p>
                    <p className="text-foreground font-normal text-sm">{product.name}</p>
                    <p className="text-muted-foreground text-xs mt-1">{product.category}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Navigation Tabs with Reasonable Font Sizes */}
      <div className="bg-background/98 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-8">
          <div className="bg-muted/20 rounded-2xl p-2 mb-6 shadow-[0_2px_8px_rgba(0,0,0,0.04)]">
            <nav className="flex gap-2">
              {[
                { id: 'overview', label: 'Overview' },
                { id: 'reviews', label: `Reviews (${reviews.length})` },
                { id: 'prices', label: 'Price Comparison' },
                { id: 'deals', label: 'Deals & Discounts' },
                { id: 'vendors', label: 'Where to Buy' },
                { id: 'stores', label: 'Store Locator' },
                { id: 'social', label: 'Community' },
                { id: 'compare', label: 'Compare' }
              ].map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 py-3 px-3 rounded-xl font-sophisticated text-sm transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'bg-background text-foreground shadow-[0_2px_6px_rgba(0,0,0,0.08)] border border-border/30'
                      : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div className="bg-background rounded-lg border border-muted p-4">
              <h2 className="text-lg font-refined text-foreground mb-4 tracking-refined">Product Rating Breakdown</h2>
              <div className="grid md:grid-cols-2 gap-4">
                {ratingParameters.map(param => (
                  <div key={param.key} className="space-y-1">
                    <div className="flex justify-between">
                      <span className="text-xs font-normal text-foreground">{param.label}</span>
                      <span className="text-xs font-normal text-muted-foreground">
                        {(() => {
                          const parameterRatings = reviews
                            .filter(review => review.parameters && param.key in review.parameters)
                            .map(review => (review.parameters as any)[param.key])
                            .filter(rating => typeof rating === 'number');

                          if (parameterRatings.length === 0) return '';

                          const average = parameterRatings.reduce((sum, rating) => sum + rating, 0) / parameterRatings.length;
                          return `${(average * 20).toFixed(0)}%`;
                        })()}
                      </span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-1">
                      <div
                        className="bg-primary h-1 rounded-full"
                        style={{
                          width: `${(() => {
                            const parameterRatings = reviews
                              .filter(review => review.parameters && param.key in review.parameters)
                              .map(review => (review.parameters as any)[param.key])
                              .filter(rating => typeof rating === 'number');

                            if (parameterRatings.length === 0) return 0;

                            const average = parameterRatings.reduce((sum, rating) => sum + rating, 0) / parameterRatings.length;
                            return average * 20;
                          })()}%`
                        }}
                      ></div>
                    </div>
                    <p className="text-xs text-muted-foreground">{param.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Reviews Tab */}
        {activeTab === 'reviews' && (
          <div className="space-y-4">
            {product ? (
              <ReviewSystem
                productId={product.id}
                productName={product.name}
                productType={product.category}
              />
            ) : (
              <div className="bg-background rounded-lg border border-muted p-6 text-center">
                <p className="text-muted-foreground">Loading reviews...</p>
              </div>
            )}
          </div>
        )}

        {/* Price Comparison Tab - Advanced affiliate monetization like Vivino */}
        {activeTab === 'prices' && (
          <div className="space-y-4">
            <div className="bg-background rounded-lg border border-muted p-4">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h2 className="text-lg font-refined text-foreground tracking-refined">Price Comparison</h2>
                  <p className="text-muted-foreground mt-1 text-sm">Find the best deals from verified vendors</p>
                </div>
                <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                  <Shield className="h-3 w-3" />
                  <span>Affiliate partnerships disclosed</span>
                </div>
              </div>
              {product ? (
                <PriceComparisonSystem
                  productId={product.id}
                  productName={product.name}
                />
              ) : (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">Loading price comparison...</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Deals Tab - Advanced discount system like Weedmaps */}
        {activeTab === 'deals' && (
          <div className="space-y-4">
            <div className="bg-background rounded-lg border border-muted p-4">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h2 className="text-lg font-refined text-foreground tracking-refined">Deals & Discounts</h2>
                  <p className="text-muted-foreground mt-1 text-sm">Exclusive offers and promo codes for {product?.name || 'this product'}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Tag className="h-5 w-5 text-primary" />
                  <span className="text-sm font-medium text-primary">Live Deals Available</span>
                </div>
              </div>
              {product ? (
                <DealsSystem
                  category={product.category}
                />
              ) : (
                <div className="text-center py-8">
                  <p className="text-primary">Loading deals...</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Vendors Tab */}
        {activeTab === 'vendors' && (
          <div className="space-y-6">
            {product ? (
              <VendorList
                productId={product.id}
                category={product.category}
              />
            ) : (
              <div className="bg-card rounded-2xl shadow-sm border border-border p-8 text-center">
                <p className="text-primary">Loading vendors...</p>
              </div>
            )}
          </div>
        )}

        {/* Stores Tab */}
        {activeTab === 'stores' && (
          <div className="space-y-6">
            {product ? (
              <StoreLocator
                productId={product.id}
                category={product.category}
              />
            ) : (
              <div className="bg-card rounded-2xl shadow-sm border border-border p-8 text-center">
                <p className="text-primary">Loading store locations...</p>
              </div>
            )}
          </div>
        )}

        {/* Social Tab - Advanced community features like Untappd */}
        {activeTab === 'social' && (
          <div className="space-y-6">
            <div className="bg-card rounded-2xl shadow-sm border border-border p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-primary">Community & Social</h2>
                  <p className="text-primary mt-1">Check-ins, achievements, and community insights for {product?.name || 'this product'}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-primary" />
                  <span className="text-sm font-medium text-primary">Join the Community</span>
                </div>
              </div>
              {product ? (
                <SocialSystem
                  productId={product.id}
                  productName={product.name}
                  productBrand={product.brand}
                />
              ) : (
                <div className="text-center py-8">
                  <p className="text-primary">Loading community features...</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Compare Tab */}
        {activeTab === 'compare' && (
          <div className="space-y-6">
            <div className="bg-card rounded-2xl shadow-sm border border-border p-6">
              <h2 className="text-2xl font-bold text-primary mb-4">Compare Similar Products</h2>
              <p className="text-primary mb-4">
                Compare this product with similar {product?.category || 'NRT'} products.
              </p>
              {product ? (
                <Link
                  to={`/products?category=${product.category}`}
                  className="bg-primary text-white px-6 py-3 rounded-2xl hover:bg-primary/90 transition-colors font-medium inline-block"
                >
                  View Similar Products
                </Link>
              ) : (
                <div className="text-center py-4">
                  <p className="text-primary">Loading comparison options...</p>
                </div>
              )}
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default ProductDetailPage;
