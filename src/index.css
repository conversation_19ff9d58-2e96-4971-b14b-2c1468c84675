/*
  ======================================================================================
  NRT LIST - ROYAL ELEGANCE DESIGN SYSTEM
  COLORS, FONTS, AND CHARACTER STYLES ONLY
  All sizing, spacing, and layout styles are hard-coded in components
  Worthy of the Queen of England, approved by <PERSON>
  ======================================================================================
*/

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* ===================================================================== */
    /* SINGLE LOGO GREEN - ROYAL ELEGANCE                                   */
    /* One shade only - sophisticated, professional, zen-like               */
    /* ===================================================================== */

    /* Base Colors - Mission Fresh Modern Palette */
    --background: 0 0% 100%;              /* Pure white background */
    --foreground: 220 13% 18%;            /* Dark gray text for readability (#1F2937) */

    /* Primary Brand - Elegant 2025 Modern Green */
    --wellness-green: 162 91% 39%;        /* Elegant modern green rgb(16, 185, 129) converted to HSL */
    --logo-green: 162 91% 39%;            /* Same as wellness green */
    --primary: 162 91% 39%;               /* Elegant modern green - 2025 sophisticated */
    --primary-foreground: 0 0% 100%;      /* Pure white text on green */

    /* Secondary Colors - Professional Grays */
    --secondary: 210 40% 98%;             /* Light gray background (#F9FAFB) */
    --secondary-foreground: 220 13% 18%;  /* Dark gray text */

    /* Muted Colors - Subtle Grays */
    --muted: 210 40% 98%;                 /* Light gray for cards (#F9FAFB) */
    --muted-foreground: 220 9% 46%;       /* Medium gray text (#6B7280) */

    /* Accent Colors - Clean & Minimal */
    --accent: 210 40% 96%;                /* Slightly darker gray for hover */
    --accent-foreground: 220 13% 18%;     /* Dark gray text */
    
    /* Status Colors - Refined & Purposeful */
    --destructive: 0 65% 51%;             /* Refined red */
    --destructive-foreground: 0 0% 100%;
    --success: 142 71% 45%;               /* Same wellness green - standardized */
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;                /* Refined amber */
    --warning-foreground: 0 0% 100%;
    --info: 142 71% 45%;                  /* Same wellness green - standardized */
    --info-foreground: 0 0% 100%;
    
    /* Sophisticated Rating App Colors - Premium Quality */
    --rating-gold: 38 75% 58%;            /* Premium gold for ratings & awards */
    --alert-red: 0 65% 56%;               /* Clean red for alerts & warnings */
    
    /* UI Element Colors - Professional Grays */
    --border: 220 13% 91%;                /* Light gray borders (#E5E7EB) */
    --input: 0 0% 100%;                   /* Pure white inputs */
    --ring: 142 71% 45%;                  /* Focus ring - standardized wellness green */
    --card: 0 0% 100%;                    /* Pure white cards */
    --card-foreground: 220 13% 18%;       /* Dark gray text */
    --popover: 0 0% 100%;                 /* Pure white popovers */
    --popover-foreground: 220 13% 18%;    /* Dark gray text */

    /* Chart Colors - Single Green Elegance */
    --chart-1: 142 71% 45%;
    --chart-2: 142 71% 45%;
    --chart-3: 142 71% 45%;
    --chart-4: 142 71% 45%;
    --chart-5: 142 71% 45%;
  }

  /* NO DARK MODE - REMOVED COMPLETELY - DARK MODE FORBIDDEN */

  /* ===================================================================== */
  /* ROYAL TYPOGRAPHY - SOPHISTICATED FONT SYSTEM                         */
  /* ===================================================================== */

  * {
    border-color: hsl(var(--border));
  }

  html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-optical-sizing: auto;
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "pnum" 1, "tnum" 0, "onum" 0, "lnum" 0, "dlig" 0;
    font-variant-numeric: proportional-nums;
    font-optical-sizing: auto;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    letter-spacing: -0.005em;
    line-height: 1.5;
    font-weight: 400;
    font-size: 16px;
  }

  /* Typography Hierarchy - Apple SF Pro Standards */
  h1, h2, h3, h4, h5, h6 {
    color: hsl(var(--foreground));
    font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Arial, sans-serif;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "pnum" 0, "tnum" 1, "onum" 0, "lnum" 1, "dlig" 0;
    font-variant-numeric: lining-nums;
    text-rendering: optimizeLegibility;
    font-optical-sizing: auto;
    margin: 0;
  }

  h1 { 
    font-weight: 590; 
    letter-spacing: -0.022em;
    line-height: 1.08349;
  }
  h2 { 
    font-weight: 590; 
    letter-spacing: -0.019em;
    line-height: 1.1034;
  }
  h3 { 
    font-weight: 590; 
    letter-spacing: -0.012em;
    line-height: 1.1364;
  }
  h4 { 
    font-weight: 590; 
    letter-spacing: -0.008em;
    line-height: 1.1765;
  }
  h5 { 
    font-weight: 510; 
    letter-spacing: -0.005em;
    line-height: 1.2273;
  }
  h6 { 
    font-weight: 510; 
    letter-spacing: -0.003em;
    line-height: 1.2857;
  }

  /* Selection - Elegant Highlight */
  ::selection {
    background: hsl(var(--primary) / 0.1);
    color: hsl(var(--foreground));
  }

  /* Focus States - Royal Elegance */
  :focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }
}

/* ===================================================================== */
/* ROYAL TYPOGRAPHY UTILITIES - CHARACTER STYLES ONLY                  */
/* ===================================================================== */

@layer components {
  /* Apple SF Pro Typography Classes - Character Styles Only */
  .tracking-royal { letter-spacing: -0.022em; }
  .tracking-elegant { letter-spacing: -0.019em; }
  .tracking-refined { letter-spacing: -0.012em; }
  .tracking-sophisticated { letter-spacing: -0.008em; }
  .tracking-graceful { letter-spacing: -0.005em; }
  .tracking-noble { letter-spacing: -0.003em; }
  
  /* Apple SF Pro Font Weight Classes */
  .font-royal { font-weight: 590; }
  .font-elegant { font-weight: 590; }
  .font-refined { font-weight: 590; }
  .font-sophisticated { font-weight: 510; }
  .font-graceful { font-weight: 510; }
  .font-noble { font-weight: 400; }
  
  /* ===================================================================== */
  /* SOPHISTICATED RATING APP COLOR SYSTEM - MINIMAL & ELEGANT             */
  /* Inspired by premium wine/beer/cigar rating apps - ONE shade per color */
  /* ===================================================================== */

  /* PRIMARY - Wellness Green (Main Brand) - ONE SHADE ONLY */
  .bg-wellness { background-color: hsl(var(--wellness-green)); }
  .text-wellness { color: hsl(var(--wellness-green)); }
  .border-wellness { border-color: hsl(var(--wellness-green)); }

  /* RATING GOLD - Premium Awards & Stars (ONE shade only) */
  .bg-rating-gold { background-color: hsl(var(--rating-gold)); }
  .text-rating-gold { color: hsl(var(--rating-gold)); }
  .border-rating-gold { border-color: hsl(var(--rating-gold)); }

  /* ALERT RED - Warnings & Critical Actions (ONE shade only) */
  .bg-alert-red { background-color: hsl(var(--alert-red)); }
  .text-alert-red { color: hsl(var(--alert-red)); }
  .border-alert-red { border-color: hsl(var(--alert-red)); }

  /* NO GRAY SYSTEM - GRAYS FORBIDDEN */
  .bg-white { background-color: hsl(var(--background)); }
  .text-white { color: hsl(var(--primary-foreground)); }
  .text-green { color: hsl(var(--wellness-green)); }

  /* Hover States - Single Shade Only - NO GRAY BACKGROUNDS */
  .hover\:bg-wellness:hover { background-color: hsl(var(--wellness-green)); }
  .hover\:text-wellness:hover { color: hsl(var(--wellness-green)); }
  .hover\:border-wellness:hover { border-color: hsl(var(--wellness-green)); }
  .hover\:text-rating-gold:hover { color: hsl(var(--rating-gold)); }
  .hover\:bg-gray:hover { background-color: hsl(var(--background)); } /* CHANGED TO WHITE */
  .hover\:text-gray-dark:hover { color: hsl(var(--foreground)); }

  /* Button Interactive States - Apple Mac Desktop Style */
  .bg-primary-hover { background-color: hsl(var(--primary) / 0.9); }
  .bg-primary-active { background-color: hsl(var(--primary) / 0.8); }
  .bg-destructive-hover { background-color: hsl(var(--destructive) / 0.9); }
  .bg-destructive-active { background-color: hsl(var(--destructive) / 0.8); }
  .bg-secondary-hover { background-color: hsl(var(--secondary) / 0.9); }
  .text-primary-hover { color: hsl(var(--primary) / 0.9); }
}

@layer utilities {
  /* Royal Scrollbar - Visual Only */
  ::-webkit-scrollbar-track {
    background: hsl(var(--background));
  }
  ::-webkit-scrollbar-thumb {
    background: hsl(var(--border));
  }
  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary));
  }
}

/* ===================================================================== */
/* APPLE MAC DESKTOP STYLE BORDER-RADIUS SYSTEM - NO MORE HARDCORNERS   */
/* Comprehensive rounded corners for all UI elements                     */
/* ===================================================================== */

@layer utilities {
  /* Force rounded corners on ALL UI elements that commonly have hardcorners */
  
  /* Buttons - Apple-style rounded corners */
  button,
  .btn,
  [role="button"],
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    border-radius: 0.75rem !important; /* 12px - Apple button standard */
  }
  
  /* Form inputs - Gentle rounded corners */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="search"],
  input[type="tel"],
  input[type="url"],
  textarea,
  select {
    border-radius: 0.5rem !important; /* 8px - Apple input standard */
  }
  
  /* Cards and containers - Professional rounded corners */
  .card,
  .bg-white,
  .bg-gray-50,
  .bg-gray-100,
  .border,
  .shadow,
  .shadow-sm,
  .shadow-md,
  .shadow-lg {
    border-radius: 1rem !important; /* 16px - Apple card standard */
  }
  
  /* Product cards and listings - Elegant rounded corners */
  .product-card,
  .listing-card,
  .review-card {
    border-radius: 1.25rem !important; /* 20px - Premium card style */
  }
  
  /* Navigation and tabs - Subtle rounded corners */
  .tab,
  .nav-item,
  .badge,
  .pill {
    border-radius: 1.5rem !important; /* 24px - Pill-shaped tabs */
  }
  
  /* Modals and dialogs - Apple-style rounded corners */
  .modal,
  .dialog,
  .popup {
    border-radius: 1.5rem !important; /* 24px - Apple modal standard */
  }
  
  /* Images and media - Gentle rounded corners */
  img,
  .image,
  .avatar {
    border-radius: 0.75rem !important; /* 12px - Refined image corners */
  }
  
  /* Override any hardcorner Tailwind classes */
  .rounded-none {
    border-radius: 0.5rem !important; /* Minimum rounding */
  }
  
  .rounded-sm {
    border-radius: 0.5rem !important;
  }
  
  .rounded {
    border-radius: 0.75rem !important;
  }
  
  .rounded-md {
    border-radius: 1rem !important;
  }
  
  .rounded-lg {
    border-radius: 1.25rem !important;
  }
  
  .rounded-xl {
    border-radius: 1.5rem !important;
  }
  
  .rounded-2xl {
    border-radius: 2rem !important;
  }
  
  /* Special Apple-style classes for premium elements */
  .rounded-apple-button {
    border-radius: 0.75rem !important;
  }
  
  .rounded-apple-card {
    border-radius: 1rem !important;
  }
  
  .rounded-apple-modal {
    border-radius: 1.5rem !important;
  }

  /* ===================================================================== */
  /* ACCESSIBILITY ENHANCEMENTS                                           */
  /* ===================================================================== */

  /* Enhanced focus indicators for better accessibility */
  button:focus-visible,
  input:focus-visible,
  select:focus-visible,
  textarea:focus-visible,
  a:focus-visible {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }

  /* Skip link for keyboard navigation */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
  }

  .skip-link:focus {
    top: 6px;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    :root {
      --primary: 162 100% 25%;
      --border: 0 0% 20%;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
  
  .rounded-apple-premium {
    border-radius: 1.25rem !important;
  }
}

/* Force border-radius on common component patterns */
div[class*="bg-"],
div[class*="border"],
section[class*="bg-"],
article[class*="bg-"] {
  border-radius: 1rem;
}

/* Ensure all interactive elements have rounded corners */
a:not(.no-radius),
button:not(.no-radius),
[role="button"]:not(.no-radius) {
  border-radius: 0.75rem;
}

/* Modern Animation Keyframes for Enhanced UI/UX */
@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(34, 197, 94, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.6);
  }
}

/* Animation Classes */
.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
  opacity: 0;
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Gradient Backgrounds for Modern Design */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}
