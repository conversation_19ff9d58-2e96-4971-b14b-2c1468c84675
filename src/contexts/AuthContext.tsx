import React, { createContext, useContext, useEffect, useState } from 'react'
import { supabase, getCurrentUser, signIn, signUp, signOut, onAuthStateChange } from '../lib/supabase'
import type { User } from '@supabase/supabase-js'

// User role types for sophisticated multi-user platform
export type UserRole = 'user' | 'vendor' | 'admin'

// Enhanced user profile interface
export interface UserProfile {
  id: string
  email: string
  full_name?: string
  role: UserRole
  vendor_id?: string
  business_name?: string
  verified_vendor?: boolean
  created_at: string
  updated_at: string
}

interface AuthContextType {
  user: User | null
  userProfile: UserProfile | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, userData?: { full_name?: string; role?: UserRole }) => Promise<void>
  signOut: () => Promise<void>
  isAuthenticated: boolean
  userRole: UserRole | null
  isVendor: boolean
  isUser: boolean
  isAdmin: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)

  // Fetch user profile with role information
  const fetchUserProfile = async (userId: string): Promise<UserProfile | null> => {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single()
      
      if (error) {
        console.error('Error fetching user profile:', error)
        // If profile doesn't exist, create a fallback user profile
        if (error.code === 'PGRST116') {
          console.log('Creating fallback user profile for:', userId)
          const { data: userData } = await supabase.auth.getUser()
          if (userData.user) {
            const fallbackProfile: UserProfile = {
              id: userId,
              email: userData.user.email || '',
              full_name: userData.user.user_metadata?.full_name || 'User',
              role: 'user' as UserRole,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
            return fallbackProfile
          }
        }
        return null
      }
      
      return data as UserProfile
    } catch (error) {
      console.error('Error in fetchUserProfile:', error)
      return null
    }
  }

  useEffect(() => {
    console.log('🚨 AuthContext: Initializing authentication...')
    
    const initializeAuth = async () => {
      try {
        // Check for existing session first
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        console.log('🚨 AuthContext: Initial session check:', session ? `Session found for ${session.user.email}` : 'No session')
        
        if (sessionError) {
          console.error('🚨 AuthContext: Session error:', sessionError)
        }
        
        if (session?.user) {
          console.log('🚨 AuthContext: Setting user from session:', session.user.email)
          setUser(session.user)
          const profile = await fetchUserProfile(session.user.id)
          console.log('🚨 AuthContext: Profile loaded from session:', profile ? 'Success' : 'Failed')
          setUserProfile(profile)
        } else {
          // Fallback to getCurrentUser if no session
          const user = await getCurrentUser()
          console.log('🚨 AuthContext: Fallback user check:', user ? `User found: ${user.email}` : 'No user found')
          setUser(user)
          if (user) {
            const profile = await fetchUserProfile(user.id)
            console.log('🚨 AuthContext: Fallback profile loaded:', profile ? 'Profile found' : 'No profile')
            setUserProfile(profile)
          }
        }
      } catch (error) {
        console.error('🚨 AuthContext: Auth initialization error:', error)
      } finally {
        setLoading(false)
      }
    }

    initializeAuth()

    // Listen for auth changes
    const { data: { subscription } } = onAuthStateChange(async (event, session) => {
      console.log('🚨 AuthContext: Auth state change event:', event, 'Session:', session ? 'Present' : 'None')
      
      try {
        const user = session?.user ?? null
        console.log('🚨 AuthContext: Setting user from event:', user ? `${user.email}` : 'null')
        setUser(user)
        
        if (user) {
          const profile = await fetchUserProfile(user.id)
          console.log('🚨 AuthContext: Profile fetched for user:', profile ? 'Success' : 'Failed')
          setUserProfile(profile)
        } else {
          setUserProfile(null)
        }
      } catch (error) {
        console.error('🚨 AuthContext: Auth state change error:', error)
      }
    })

    return () => {
      console.log('🚨 AuthContext: Cleaning up auth subscription')
      subscription.unsubscribe()
    }
  }, [])

  const handleSignIn = async (email: string, password: string) => {
    console.log('🚨 AuthContext: Starting sign in for:', email)
    setLoading(true)
    try {
      const result = await signIn(email, password)
      console.log('🚨 AuthContext: Sign in successful:', result ? 'Success' : 'No result')
      console.log('🚨 AuthContext: Session after sign in:', result?.session ? 'Session present' : 'No session')
      
      // Immediately update auth state with the returned session
      if (result?.session?.user) {
        console.log('🚨 AuthContext: Immediately setting user from sign in result:', result.session.user.email)
        setUser(result.session.user)
        const profile = await fetchUserProfile(result.session.user.id)
        console.log('🚨 AuthContext: Profile loaded immediately after sign in:', profile ? 'Success' : 'Failed')
        setUserProfile(profile)
      }
    } catch (error) {
      console.log('🚨 AuthContext: Sign in error:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const handleSignUp = async (email: string, password: string, userData?: { full_name?: string; role?: UserRole }) => {
    setLoading(true)
    try {
      await signUp(email, password, userData)
    } catch (error) {
      throw error
    } finally {
      setLoading(false)
    }
  }

  const handleSignOut = async () => {
    setLoading(true)
    try {
      await signOut()
    } catch (error) {
      throw error
    } finally {
      setLoading(false)
    }
  }

  const value = {
    user,
    userProfile,
    loading,
    signIn: handleSignIn,
    signUp: handleSignUp,
    signOut: handleSignOut,
    isAuthenticated: !!user,
    userRole: userProfile?.role || null,
    isVendor: userProfile?.role === 'vendor',
    isUser: userProfile?.role === 'user',
    isAdmin: userProfile?.role === 'admin'
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export default AuthProvider
