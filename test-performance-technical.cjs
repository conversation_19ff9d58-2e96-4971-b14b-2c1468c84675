const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  try {
    console.log('⚡ PERFORMANCE AND TECHNICAL ANALYSIS - localhost:5002');
    
    // Enable console logging
    page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      if (type === 'error') {
        console.log(`🚨 CONSOLE ERROR: ${text}`);
      } else if (type === 'warning') {
        console.log(`⚠️ CONSOLE WARNING: ${text}`);
      } else if (type === 'log' && text.includes('error')) {
        console.log(`📝 CONSOLE LOG (error-related): ${text}`);
      }
    });
    
    // Test performance on key pages
    const performancePages = [
      { name: 'Landing Page', url: '/' },
      { name: 'About Page', url: '/about' },
      { name: 'Contact Page', url: '/contact' },
      { name: '<PERSON> Page', url: '/reviews' },
      { name: 'NRT Directory', url: '/nrt' }
    ];
    
    console.log('\\n📊 Testing Performance:');
    
    for (const pageInfo of performancePages) {
      try {
        console.log(`\\n🔍 Testing: ${pageInfo.name} (${pageInfo.url})`);
        
        const startTime = Date.now();
        
        // Navigate and measure load time
        await page.goto(`http://localhost:5002${pageInfo.url}`, { 
          waitUntil: 'domcontentloaded', 
          timeout: 15000 
        });
        
        const domLoadTime = Date.now() - startTime;
        
        // Wait for additional content to load
        await new Promise(resolve => setTimeout(resolve, 2000));
        const totalLoadTime = Date.now() - startTime;
        
        // Get performance metrics
        const performanceMetrics = await page.evaluate(() => {
          const navigation = performance.getEntriesByType('navigation')[0];
          const resources = performance.getEntriesByType('resource');
          
          return {
            domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
            loadComplete: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
            resourceCount: resources.length,
            totalResourceSize: resources.reduce((total, resource) => total + (resource.transferSize || 0), 0),
            slowResources: resources.filter(resource => resource.duration > 1000).length
          };
        });
        
        // Check for memory usage and DOM size
        const domMetrics = await page.evaluate(() => {
          const elements = document.querySelectorAll('*');
          const images = document.querySelectorAll('img');
          const scripts = document.querySelectorAll('script');
          const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
          
          return {
            totalElements: elements.length,
            images: images.length,
            scripts: scripts.length,
            stylesheets: stylesheets.length,
            bodySize: document.body.innerHTML.length
          };
        });
        
        console.log(`  ⏱️ DOM Load Time: ${domLoadTime}ms`);
        console.log(`  ⏱️ Total Load Time: ${totalLoadTime}ms`);
        console.log(`  📊 DOM Elements: ${domMetrics.totalElements}`);
        console.log(`  🖼️ Images: ${domMetrics.images}`);
        console.log(`  📜 Scripts: ${domMetrics.scripts}`);
        console.log(`  🎨 Stylesheets: ${domMetrics.stylesheets}`);
        console.log(`  📦 Body Size: ${Math.round(domMetrics.bodySize / 1024)}KB`);
        console.log(`  🌐 Resources: ${performanceMetrics.resourceCount}`);
        console.log(`  📈 Resource Size: ${Math.round(performanceMetrics.totalResourceSize / 1024)}KB`);
        console.log(`  🐌 Slow Resources: ${performanceMetrics.slowResources}`);
        
        // Performance assessment
        if (totalLoadTime > 5000) {
          console.log(`  ❌ SLOW: Load time > 5 seconds`);
        } else if (totalLoadTime > 3000) {
          console.log(`  ⚠️ MODERATE: Load time > 3 seconds`);
        } else {
          console.log(`  ✅ FAST: Load time < 3 seconds`);
        }
        
        if (domMetrics.totalElements > 2000) {
          console.log(`  ⚠️ HEAVY DOM: ${domMetrics.totalElements} elements`);
        } else {
          console.log(`  ✅ LIGHT DOM: ${domMetrics.totalElements} elements`);
        }
        
      } catch (error) {
        console.log(`❌ Error testing ${pageInfo.name}: ${error.message}`);
      }
    }
    
    // Test console errors on different pages
    console.log('\\n🚨 Console Error Analysis:');
    
    let totalErrors = 0;
    let totalWarnings = 0;
    
    const errorCheckPages = ['/', '/about', '/contact', '/reviews'];
    
    for (const url of errorCheckPages) {
      try {
        console.log(`\\n🔍 Checking console errors on: ${url}`);
        
        const errors = [];
        const warnings = [];
        
        page.on('console', msg => {
          if (msg.type() === 'error') {
            errors.push(msg.text());
          } else if (msg.type() === 'warning') {
            warnings.push(msg.text());
          }
        });
        
        await page.goto(`http://localhost:5002${url}`, { waitUntil: 'domcontentloaded' });
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log(`  🚨 Errors: ${errors.length}`);
        console.log(`  ⚠️ Warnings: ${warnings.length}`);
        
        if (errors.length > 0) {
          console.log(`  📝 Error details: ${errors.slice(0, 3).join('; ')}`);
        }
        
        totalErrors += errors.length;
        totalWarnings += warnings.length;
        
      } catch (error) {
        console.log(`❌ Error checking console on ${url}: ${error.message}`);
      }
    }
    
    console.log(`\\n📊 Console Summary:`);
    console.log(`  🚨 Total Errors: ${totalErrors}`);
    console.log(`  ⚠️ Total Warnings: ${totalWarnings}`);
    
    if (totalErrors === 0) {
      console.log(`  ✅ No console errors detected`);
    } else {
      console.log(`  ❌ Console errors need attention`);
    }
    
    console.log('\\n⚡ PERFORMANCE AND TECHNICAL ANALYSIS COMPLETE');
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  
  await browser.close();
})();
