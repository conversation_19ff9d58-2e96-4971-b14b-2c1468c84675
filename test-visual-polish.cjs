const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  try {
    console.log('✨ FINAL VISUAL POLISH AND ELEGANCE TESTING - localhost:5002');
    
    // Test visual consistency across key pages
    const visualPages = [
      { name: 'Landing Page', url: '/' },
      { name: 'About Page', url: '/about' },
      { name: 'Contact Page', url: '/contact' },
      { name: 'Reviews Page', url: '/reviews' },
      { name: 'Terms Page', url: '/terms' },
      { name: 'Privacy Page', url: '/privacy' }
    ];
    
    console.log('\\n🎨 Testing Visual Consistency:');
    
    for (const pageInfo of visualPages) {
      try {
        console.log(`\\n🔍 Testing: ${pageInfo.name} (${pageInfo.url})`);
        
        await page.goto(`http://localhost:5002${pageInfo.url}`, { waitUntil: 'domcontentloaded', timeout: 15000 });
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Check visual elements and styling
        const visualAnalysis = await page.evaluate(() => {
          const body = document.body;
          const computedStyle = window.getComputedStyle(body);
          
          // Check color usage
          const allElements = document.querySelectorAll('*');
          const colors = new Set();
          const backgrounds = new Set();
          const borders = new Set();
          
          Array.from(allElements).forEach(el => {
            const style = window.getComputedStyle(el);
            if (style.color && style.color !== 'rgba(0, 0, 0, 0)') colors.add(style.color);
            if (style.backgroundColor && style.backgroundColor !== 'rgba(0, 0, 0, 0)') backgrounds.add(style.backgroundColor);
            if (style.borderColor && style.borderColor !== 'rgba(0, 0, 0, 0)') borders.add(style.borderColor);
          });
          
          // Check for hardcoded colors (not CSS variables)
          const hardcodedColors = [];
          Array.from(allElements).forEach(el => {
            const style = el.getAttribute('style');
            if (style && (style.includes('#') || style.includes('rgb') || style.includes('hsl'))) {
              hardcodedColors.push(style);
            }
          });
          
          // Check typography consistency
          const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
          const fonts = new Set();
          Array.from(headings).forEach(h => {
            const style = window.getComputedStyle(h);
            fonts.add(style.fontFamily);
          });
          
          // Check for Apple Mac desktop styling elements
          const roundedElements = Array.from(allElements).filter(el => {
            const style = window.getComputedStyle(el);
            return style.borderRadius && style.borderRadius !== '0px';
          });
          
          const shadowElements = Array.from(allElements).filter(el => {
            const style = window.getComputedStyle(el);
            return style.boxShadow && style.boxShadow !== 'none';
          });
          
          // Check for cheap/unprofessional elements
          const content = body.innerText.toLowerCase();
          const cheapIndicators = [
            'awesome', 'amazing', 'incredible', 'fantastic', 'super',
            '!!!', 'wow', 'omg', 'lol', 'best ever', 'mind-blowing'
          ];
          const foundCheapWords = cheapIndicators.filter(word => content.includes(word));
          
          return {
            uniqueColors: colors.size,
            uniqueBackgrounds: backgrounds.size,
            uniqueBorders: borders.size,
            hardcodedColorsCount: hardcodedColors.length,
            hardcodedColorsSample: hardcodedColors.slice(0, 3),
            uniqueFonts: fonts.size,
            fontFamilies: Array.from(fonts),
            roundedElementsCount: roundedElements.length,
            shadowElementsCount: shadowElements.length,
            cheapWordsFound: foundCheapWords,
            hasContent: body.innerText.length > 100,
            contentLength: body.innerText.length
          };
        });
        
        console.log(`📊 Content Length: ${visualAnalysis.contentLength} characters`);
        
        if (visualAnalysis.hasContent) {
          console.log(`  🎨 Unique Colors: ${visualAnalysis.uniqueColors}`);
          console.log(`  🖼️ Unique Backgrounds: ${visualAnalysis.uniqueBackgrounds}`);
          console.log(`  🔲 Unique Borders: ${visualAnalysis.uniqueBorders}`);
          console.log(`  ❌ Hardcoded Colors: ${visualAnalysis.hardcodedColorsCount} ${visualAnalysis.hardcodedColorsCount > 0 ? '(VIOLATION)' : '(Clean)'}`);
          if (visualAnalysis.hardcodedColorsSample.length > 0) {
            console.log(`    📝 Sample: ${visualAnalysis.hardcodedColorsSample.join('; ')}`);
          }
          console.log(`  📝 Font Families: ${visualAnalysis.uniqueFonts} (${visualAnalysis.fontFamilies.join(', ')})`);
          console.log(`  🔘 Rounded Elements: ${visualAnalysis.roundedElementsCount}`);
          console.log(`  🌟 Shadow Elements: ${visualAnalysis.shadowElementsCount}`);
          console.log(`  💸 Cheap Words: ${visualAnalysis.cheapWordsFound.length} ${visualAnalysis.cheapWordsFound.length > 0 ? '(UNPROFESSIONAL)' : '(Professional)'}`);
          if (visualAnalysis.cheapWordsFound.length > 0) {
            console.log(`    📝 Found: ${visualAnalysis.cheapWordsFound.join(', ')}`);
          }
          
          // Visual assessment
          if (visualAnalysis.hardcodedColorsCount > 0) {
            console.log(`  ❌ VIOLATION: Hardcoded colors found`);
          } else {
            console.log(`  ✅ CLEAN: No hardcoded colors`);
          }
          
          if (visualAnalysis.cheapWordsFound.length > 0) {
            console.log(`  ❌ UNPROFESSIONAL: Cheap language detected`);
          } else {
            console.log(`  ✅ PROFESSIONAL: Clean, elegant language`);
          }
          
          if (visualAnalysis.roundedElementsCount > 0 && visualAnalysis.shadowElementsCount > 0) {
            console.log(`  ✅ APPLE STYLE: Rounded corners and shadows detected`);
          } else {
            console.log(`  ⚠️ BASIC STYLE: Limited Apple Mac desktop styling`);
          }
          
        } else {
          console.log(`⚠️ Page loaded but minimal content`);
        }
        
        // Take screenshot for visual review
        const filename = `visual-${pageInfo.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}.png`;
        await page.screenshot({ path: filename, fullPage: false });
        console.log(`📸 Screenshot saved: ${filename}`);
        
      } catch (error) {
        console.log(`❌ Error testing ${pageInfo.name}: ${error.message}`);
      }
    }
    
    // Test index.css compliance
    console.log('\\n📋 Testing index.css Color Compliance:');
    
    try {
      await page.goto('http://localhost:5002', { waitUntil: 'domcontentloaded' });
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check CSS variables usage
      const cssVariablesCheck = await page.evaluate(() => {
        const rootStyles = window.getComputedStyle(document.documentElement);
        const cssVariables = [];
        
        // Get all CSS custom properties
        for (let i = 0; i < rootStyles.length; i++) {
          const property = rootStyles[i];
          if (property.startsWith('--')) {
            cssVariables.push({
              property: property,
              value: rootStyles.getPropertyValue(property).trim()
            });
          }
        }
        
        return {
          cssVariablesCount: cssVariables.length,
          cssVariables: cssVariables.slice(0, 10) // First 10 for sample
        };
      });
      
      console.log(`🎨 CSS Variables Found: ${cssVariablesCheck.cssVariablesCount}`);
      if (cssVariablesCheck.cssVariables.length > 0) {
        console.log(`📝 Sample Variables:`);
        cssVariablesCheck.cssVariables.forEach(variable => {
          console.log(`  ${variable.property}: ${variable.value}`);
        });
      }
      
      if (cssVariablesCheck.cssVariablesCount > 0) {
        console.log(`✅ CSS VARIABLES: Color system detected`);
      } else {
        console.log(`❌ NO CSS VARIABLES: Missing color system`);
      }
      
    } catch (error) {
      console.log(`❌ Error checking CSS variables: ${error.message}`);
    }
    
    console.log('\\n✨ FINAL VISUAL POLISH AND ELEGANCE TESTING COMPLETE');
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  
  await browser.close();
})();
