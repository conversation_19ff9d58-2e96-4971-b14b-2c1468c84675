const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  try {
    // Set mobile viewport
    await page.setViewport({ width: 375, height: 667 });
    
    console.log('📱 Testing mobile menu on localhost:5002...');
    await page.goto('http://localhost:5002', { waitUntil: 'domcontentloaded', timeout: 30000 });
    
    // Take screenshot of mobile view
    await page.screenshot({ path: 'mobile-menu-closed.png', fullPage: false });
    console.log('📸 Mobile view screenshot saved: mobile-menu-closed.png');
    
    // Look for mobile menu button
    const menuButton = await page.$('button[aria-label*="menu"], button[aria-label*="Menu"], button[aria-label*="navigation"]');
    
    if (menuButton) {
      console.log('✅ Mobile menu button found');
      
      // Click the menu button
      await menuButton.click();
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Take screenshot with menu open
      await page.screenshot({ path: 'mobile-menu-open.png', fullPage: false });
      console.log('📸 Mobile menu open screenshot saved: mobile-menu-open.png');
      
      // Check if menu is visible
      const menuVisible = await page.evaluate(() => {
        const menus = document.querySelectorAll('[class*="mobile"], div[class*="md:hidden"]');
        return Array.from(menus).some(menu => 
          menu.offsetParent !== null && 
          menu.style.display !== 'none' &&
          menu.getBoundingClientRect().height > 0
        );
      });
      
      if (menuVisible) {
        console.log('✅ Mobile menu opens successfully');
      } else {
        console.log('❌ Mobile menu button clicked but menu not visible');
      }
      
      // Test menu links
      const menuLinks = await page.$$eval('a', links => 
        links.filter(link => link.closest('[class*="mobile"], div[class*="md:hidden"]')).length
      );
      console.log(`📋 Found ${menuLinks} menu links`);
      
    } else {
      console.log('❌ Mobile menu button not found');
    }
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  
  await browser.close();
})();
