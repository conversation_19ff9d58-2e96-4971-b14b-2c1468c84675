const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function takeComprehensiveScreenshots() {
  console.log('🚀 Starting comprehensive app inspection...');
  
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: { width: 1200, height: 800 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Set viewport for desktop
  await page.setViewport({ width: 1200, height: 800 });
  
  const baseUrl = 'http://localhost:5002';
  
  // List of all routes to test
  const routes = [
    { path: '/', name: 'homepage' },
    { path: '/nrt', name: 'nrt-directory' },
    { path: '/products', name: 'products-page' },
    { path: '/brands', name: 'brands-page' },
    { path: '/stores', name: 'stores-page' },
    { path: '/vendors', name: 'vendors-page' },
    { path: '/store-locator', name: 'store-locator' },
    { path: '/search', name: 'search-page' },
    { path: '/discover', name: 'discover-page' },
    { path: '/reviews', name: 'reviews-page' },
    { path: '/deals', name: 'deals-page' },
    { path: '/price-compare', name: 'price-compare' },
    { path: '/smokeless', name: 'smokeless-page' },
    { path: '/retailers', name: 'retailers-page' },
    { path: '/community', name: 'community-page' },
    { path: '/progress', name: 'progress-page' },
    { path: '/profile', name: 'profile-page' },
    { path: '/about', name: 'about-page' },
    { path: '/contact', name: 'contact-page' },
    { path: '/privacy', name: 'privacy-page' },
    { path: '/terms', name: 'terms-page' }
  ];
  
  const screenshots = [];
  
  for (const route of routes) {
    try {
      console.log(`📸 Taking screenshot of ${route.name} (${route.path})`);
      
      await page.goto(`${baseUrl}${route.path}`, { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      });
      
      // Wait a bit for any animations or dynamic content
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Take screenshot
      const screenshotPath = `inspection-${route.name}.png`;
      await page.screenshot({ 
        path: screenshotPath,
        fullPage: true 
      });
      
      // Get page title and any console errors
      const title = await page.title();
      const errors = await page.evaluate(() => {
        return window.console.errors || [];
      });
      
      screenshots.push({
        route: route.path,
        name: route.name,
        screenshot: screenshotPath,
        title: title,
        errors: errors
      });
      
      console.log(`✅ Screenshot saved: ${screenshotPath}`);
      
    } catch (error) {
      console.error(`❌ Error taking screenshot of ${route.name}:`, error.message);
      screenshots.push({
        route: route.path,
        name: route.name,
        screenshot: null,
        error: error.message
      });
    }
  }
  
  // Save inspection report
  const report = {
    timestamp: new Date().toISOString(),
    screenshots: screenshots,
    summary: {
      total: routes.length,
      successful: screenshots.filter(s => s.screenshot).length,
      failed: screenshots.filter(s => s.error).length
    }
  };
  
  fs.writeFileSync('inspection-report.json', JSON.stringify(report, null, 2));
  console.log('📋 Inspection report saved: inspection-report.json');
  
  await browser.close();
  console.log('🎉 Comprehensive inspection complete!');
  
  return report;
}

// Run the inspection
takeComprehensiveScreenshots().catch(console.error);
