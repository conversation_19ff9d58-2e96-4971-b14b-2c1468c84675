// Create and populate mission_fresh.nrt_products table with FDA-approved NRT products
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://yekarqanirdkdckimpna.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc'

const supabase = createClient(supabaseUrl, supabaseKey, {
  db: { schema: 'mission_fresh' }
})

// FDA-approved NRT products data
const nrtProducts = [
  {
    name: 'Nicorette Gum 2mg Original',
    brand: 'Nicorette',
    category: 'gum',
    description: 'Sugar-free nicotine gum that helps reduce withdrawal symptoms and cravings when quitting smoking. FDA-approved for smoking cessation.',
    image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400',
    user_rating_avg: 4.2,
    user_rating_count: 2847,
    nicotine_strengths: ['2mg', '4mg'],
    flavors: ['Original', 'Mint', 'Cinnamon', 'Fruit'],
    fda_approved: true,
    form: 'gum'
  },
  {
    name: 'Nicorette Gum 4mg Mint',
    brand: 'Nicorette',
    category: 'gum',
    description: 'Higher strength nicotine gum for heavy smokers. Mint flavored for fresh breath while quitting smoking.',
    image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400',
    user_rating_avg: 4.3,
    user_rating_count: 1923,
    nicotine_strengths: ['4mg'],
    flavors: ['Mint'],
    fda_approved: true,
    form: 'gum'
  },
  {
    name: 'NicoDerm CQ Step 1 Clear Patch 21mg',
    brand: 'NicoDerm CQ',
    category: 'patch',
    description: 'Clear nicotine patch for 24-hour craving control. Step 1 for heavy smokers (1+ pack per day).',
    image_url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400',
    user_rating_avg: 4.1,
    user_rating_count: 3764,
    nicotine_strengths: ['21mg'],
    flavors: ['Clear'],
    fda_approved: true,
    form: 'patch'
  },
  {
    name: 'NicoDerm CQ Step 2 Clear Patch 14mg',
    brand: 'NicoDerm CQ',
    category: 'patch',
    description: 'Clear nicotine patch for 24-hour craving control. Step 2 for moderate smokers.',
    image_url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400',
    user_rating_avg: 4.0,
    user_rating_count: 2156,
    nicotine_strengths: ['14mg'],
    flavors: ['Clear'],
    fda_approved: true,
    form: 'patch'
  },
  {
    name: 'NicoDerm CQ Step 3 Clear Patch 7mg',
    brand: 'NicoDerm CQ',
    category: 'patch',
    description: 'Clear nicotine patch for 24-hour craving control. Step 3 for light smokers and final step.',
    image_url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400',
    user_rating_avg: 3.9,
    user_rating_count: 1847,
    nicotine_strengths: ['7mg'],
    flavors: ['Clear'],
    fda_approved: true,
    form: 'patch'
  },
  {
    name: 'Commit Nicotine Lozenges 2mg',
    brand: 'Commit',
    category: 'lozenge',
    description: 'Sugar-free nicotine lozenges that dissolve slowly to help control cravings throughout the day.',
    image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400',
    user_rating_avg: 4.4,
    user_rating_count: 1632,
    nicotine_strengths: ['2mg', '4mg'],
    flavors: ['Original', 'Mint', 'Cherry'],
    fda_approved: true,
    form: 'lozenge'
  },
  {
    name: 'Commit Nicotine Lozenges 4mg',
    brand: 'Commit',
    category: 'lozenge',
    description: 'Higher strength nicotine lozenges for heavy smokers who smoke within 30 minutes of waking.',
    image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400',
    user_rating_avg: 4.3,
    user_rating_count: 987,
    nicotine_strengths: ['4mg'],
    flavors: ['Original', 'Mint'],
    fda_approved: true,
    form: 'lozenge'
  },
  {
    name: 'Nicorette QuickMist Mouth Spray 1mg',
    brand: 'Nicorette',
    category: 'spray',
    description: 'Fast-acting nicotine mouth spray for immediate craving relief. Delivers nicotine quickly through mouth lining.',
    image_url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400',
    user_rating_avg: 4.0,
    user_rating_count: 743,
    nicotine_strengths: ['1mg'],
    flavors: ['Mint'],
    fda_approved: true,
    form: 'spray'
  },
  {
    name: 'Nicorette Inhaler 10mg Cartridge',
    brand: 'Nicorette',
    category: 'inhaler',
    description: 'Nicotine inhaler that satisfies the hand-to-mouth habit while delivering nicotine vapor.',
    image_url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400',
    user_rating_avg: 3.8,
    user_rating_count: 564,
    nicotine_strengths: ['10mg'],
    flavors: ['Menthol'],
    fda_approved: true,
    form: 'inhaler'
  },
  {
    name: 'Habitrol Nicotine Patch 21mg',
    brand: 'Habitrol',
    category: 'patch',
    description: 'Generic nicotine patch providing 24-hour smoking cessation support. Affordable alternative to brand names.',
    image_url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400',
    user_rating_avg: 3.9,
    user_rating_count: 1456,
    nicotine_strengths: ['21mg', '14mg', '7mg'],
    flavors: ['Clear'],
    fda_approved: true,
    form: 'patch'
  },
  {
    name: 'Thrive Nicotine Gum 2mg',
    brand: 'Thrive',
    category: 'gum',
    description: 'Affordable nicotine gum alternative with same active ingredient as leading brands.',
    image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400',
    user_rating_avg: 4.1,
    user_rating_count: 892,
    nicotine_strengths: ['2mg', '4mg'],
    flavors: ['Original', 'Mint'],
    fda_approved: true,
    form: 'gum'
  },
  {
    name: 'GoodSense Nicotine Lozenges 2mg',
    brand: 'GoodSense',
    category: 'lozenge',
    description: 'Generic nicotine lozenges providing the same cessation benefits at a lower cost.',
    image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400',
    user_rating_avg: 4.0,
    user_rating_count: 627,
    nicotine_strengths: ['2mg', '4mg'],
    flavors: ['Original', 'Mint'],
    fda_approved: true,
    form: 'lozenge'
  },
  {
    name: 'Equate Nicotine Patch 14mg',
    brand: 'Equate',
    category: 'patch',
    description: 'Walmart brand nicotine patch offering quality cessation support at an affordable price.',
    image_url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400',
    user_rating_avg: 3.8,
    user_rating_count: 734,
    nicotine_strengths: ['21mg', '14mg', '7mg'],
    flavors: ['Clear'],
    fda_approved: true,
    form: 'patch'
  },
  {
    name: 'CVS Health Nicotine Gum 4mg',
    brand: 'CVS Health',
    category: 'gum',
    description: 'CVS store brand nicotine gum with proven effectiveness for smoking cessation.',
    image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400',
    user_rating_avg: 4.2,
    user_rating_count: 513,
    nicotine_strengths: ['2mg', '4mg'],
    flavors: ['Original', 'Mint', 'Fruit'],
    fda_approved: true,
    form: 'gum'
  },
  {
    name: 'Walgreens Nicotine Lozenges 4mg',
    brand: 'Walgreens',
    category: 'lozenge',
    description: 'Walgreens brand nicotine lozenges for effective craving control and cessation support.',
    image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400',
    user_rating_avg: 3.9,
    user_rating_count: 445,
    nicotine_strengths: ['2mg', '4mg'],
    flavors: ['Original', 'Mint', 'Cherry'],
    fda_approved: true,
    form: 'lozenge'
  },
  {
    name: 'Target Up & Up Nicotine Patch 7mg',
    brand: 'Up & Up',
    category: 'patch',
    description: 'Target store brand nicotine patch for final step cessation or light smokers.',
    image_url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400',
    user_rating_avg: 3.7,
    user_rating_count: 289,
    nicotine_strengths: ['21mg', '14mg', '7mg'],
    flavors: ['Clear'],
    fda_approved: true,
    form: 'patch'
  },
  {
    name: 'Rite Aid Nicotine Gum 2mg Cinnamon',
    brand: 'Rite Aid',
    category: 'gum',
    description: 'Rite Aid brand cinnamon flavored nicotine gum for pleasant tasting cessation support.',
    image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400',
    user_rating_avg: 4.0,
    user_rating_count: 356,
    nicotine_strengths: ['2mg', '4mg'],
    flavors: ['Cinnamon', 'Original', 'Mint'],
    fda_approved: true,
    form: 'gum'
  },
  {
    name: 'Amazon Basic Care Nicotine Lozenges',
    brand: 'Amazon Basic Care',
    category: 'lozenge',
    description: 'Amazon brand nicotine lozenges offering quality cessation support with fast shipping.',
    image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400',
    user_rating_avg: 4.1,
    user_rating_count: 672,
    nicotine_strengths: ['2mg', '4mg'],
    flavors: ['Original', 'Mint'],
    fda_approved: true,
    form: 'lozenge'
  },
  {
    name: 'Kroger Nicotine Patch 21mg',
    brand: 'Kroger',
    category: 'patch',
    description: 'Kroger store brand nicotine patch for heavy smokers starting their quit journey.',
    image_url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400',
    user_rating_avg: 3.8,
    user_rating_count: 203,
    nicotine_strengths: ['21mg', '14mg', '7mg'],
    flavors: ['Clear'],
    fda_approved: true,
    form: 'patch'
  },
  {
    name: 'Safeway Signature Care Nicotine Gum',
    brand: 'Safeway',
    category: 'gum',
    description: 'Safeway brand nicotine gum providing effective cessation support at grocery store convenience.',
    image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400',
    user_rating_avg: 3.9,
    user_rating_count: 178,
    nicotine_strengths: ['2mg', '4mg'],
    flavors: ['Original', 'Mint'],
    fda_approved: true,
    form: 'gum'
  },
  {
    name: 'Great Value Nicotine Patch',
    brand: 'Great Value',
    category: 'patch',
    description: 'Walmart Great Value brand nicotine patch offering affordable cessation support.',
    image_url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400',
    user_rating_avg: 3.6,
    user_rating_count: 294,
    nicotine_strengths: ['21mg', '14mg', '7mg'],
    flavors: ['Clear'],
    fda_approved: true,
    form: 'patch'
  },
  {
    name: 'Kirkland Signature Nicotine Gum',
    brand: 'Kirkland',
    category: 'gum',
    description: 'Costco Kirkland brand nicotine gum offering bulk quantity savings for cessation support.',
    image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400',
    user_rating_avg: 4.3,
    user_rating_count: 856,
    nicotine_strengths: ['2mg', '4mg'],
    flavors: ['Original', 'Mint', 'Fruit'],
    fda_approved: true,
    form: 'gum'
  },
  {
    name: 'Member\'s Mark Nicotine Lozenges',
    brand: 'Member\'s Mark',
    category: 'lozenge',
    description: 'Sam\'s Club brand nicotine lozenges providing bulk quantity value for cessation.',
    image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400',
    user_rating_avg: 4.0,
    user_rating_count: 429,
    nicotine_strengths: ['2mg', '4mg'],
    flavors: ['Original', 'Mint', 'Cherry'],
    fda_approved: true,
    form: 'lozenge'
  },
  {
    name: 'ShopRite Nicotine Patch',
    brand: 'ShopRite',
    category: 'patch',
    description: 'ShopRite grocery store brand nicotine patch for convenient cessation support.',
    image_url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400',
    user_rating_avg: 3.7,
    user_rating_count: 156,
    nicotine_strengths: ['21mg', '14mg', '7mg'],
    flavors: ['Clear'],
    fda_approved: true,
    form: 'patch'
  },
  {
    name: 'Publix Nicotine Gum',
    brand: 'Publix',
    category: 'gum',
    description: 'Publix grocery store brand nicotine gum for southern U.S. cessation support.',
    image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400',
    user_rating_avg: 4.1,
    user_rating_count: 267,
    nicotine_strengths: ['2mg', '4mg'],
    flavors: ['Original', 'Mint'],
    fda_approved: true,
    form: 'gum'
  },
  {
    name: 'Health Mart Nicotine Lozenges',
    brand: 'Health Mart',
    category: 'lozenge',
    description: 'Independent pharmacy brand nicotine lozenges with personalized cessation counseling.',
    image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400',
    user_rating_avg: 4.2,
    user_rating_count: 134,
    nicotine_strengths: ['2mg', '4mg'],
    flavors: ['Original', 'Mint'],
    fda_approved: true,
    form: 'lozenge'
  }
];

async function createAndPopulateNRTProductsTable() {
  try {
    console.log('🚨 Creating mission_fresh.nrt_products table...');
    
    // Create the table
    const { error: createError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS mission_fresh.nrt_products (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          name TEXT NOT NULL,
          brand TEXT NOT NULL,
          category TEXT NOT NULL,
          description TEXT,
          image_url TEXT,
          user_rating_avg DECIMAL(3,2),
          user_rating_count INTEGER DEFAULT 0,
          nicotine_strengths JSONB DEFAULT '[]'::jsonb,
          flavors JSONB DEFAULT '[]'::jsonb,
          fda_approved BOOLEAN DEFAULT true,
          form TEXT,
          ingredients TEXT,
          country_of_origin TEXT,
          manufacturer TEXT,
          tags JSONB DEFAULT '[]'::jsonb,
          expert_notes_chemicals TEXT,
          expert_notes_gum_health TEXT,
          created_at TIMESTAMPTZ DEFAULT now(),
          updated_at TIMESTAMPTZ DEFAULT now()
        );
        
        -- Create indexes for performance
        CREATE INDEX IF NOT EXISTS idx_nrt_products_category ON mission_fresh.nrt_products(category);
        CREATE INDEX IF NOT EXISTS idx_nrt_products_brand ON mission_fresh.nrt_products(brand);
        CREATE INDEX IF NOT EXISTS idx_nrt_products_fda_approved ON mission_fresh.nrt_products(fda_approved);
        CREATE INDEX IF NOT EXISTS idx_nrt_products_form ON mission_fresh.nrt_products(form);
      `
    });

    if (createError) {
      throw createError;
    }

    console.log('✅ Table created successfully');

    // Clear existing data
    const { error: deleteError } = await supabase
      .from('nrt_products')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all

    if (deleteError) {
      console.log('Note: No existing data to clear');
    }

    // Insert NRT products
    console.log('🚨 Inserting', nrtProducts.length, 'FDA-approved NRT products...');
    
    const { data, error: insertError } = await supabase
      .from('nrt_products')
      .insert(nrtProducts)
      .select();

    if (insertError) {
      throw insertError;
    }

    console.log('✅ Successfully inserted', data?.length || 0, 'NRT products');
    
    // Verify the data
    const { data: verifyData, error: verifyError } = await supabase
      .from('nrt_products')
      .select('id, name, brand, category, fda_approved')
      .order('name');

    if (verifyError) {
      throw verifyError;
    }

    console.log('✅ Verification: Found', verifyData?.length || 0, 'NRT products in database');
    console.log('Sample products:');
    verifyData?.slice(0, 5).forEach(product => {
      console.log(`  - ${product.name} (${product.brand}) - ${product.category} - FDA: ${product.fda_approved}`);
    });

  } catch (error) {
    console.error('❌ Error creating NRT products table:', error);
    throw error;
  }
}

// Run the function
createAndPopulateNRTProductsTable()
  .then(() => {
    console.log('🎉 NRT products table setup complete!');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Setup failed:', error);
    process.exit(1);
  });
