const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  try {
    console.log('🔐 AUTHENTICATION MODAL TESTING - localhost:5002');
    
    await page.goto('http://localhost:5002', { waitUntil: 'domcontentloaded' });
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check for authentication buttons with corrected selectors
    const authButtons = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const signInButtons = buttons.filter(btn => 
        btn.textContent.toLowerCase().includes('sign in') || 
        btn.textContent.toLowerCase().includes('login')
      );
      const signUpButtons = buttons.filter(btn => 
        btn.textContent.toLowerCase().includes('sign up') || 
        btn.textContent.toLowerCase().includes('register')
      );
      
      return {
        signInFound: signInButtons.length > 0,
        signUpFound: signUpButtons.length > 0,
        signInText: signInButtons.map(btn => btn.textContent.trim()),
        signUpText: signUpButtons.map(btn => btn.textContent.trim()),
        totalButtons: buttons.length
      };
    });
    
    console.log(`📊 Total buttons found: ${authButtons.totalButtons}`);
    console.log(`🔐 Sign In buttons: ${authButtons.signInFound ? '✅' : '❌'} (${authButtons.signInText.join(', ')})`);
    console.log(`📝 Sign Up buttons: ${authButtons.signUpFound ? '✅' : '❌'} (${authButtons.signUpText.join(', ')})`);
    
    if (authButtons.signInFound) {
      try {
        // Click the first Sign In button
        await page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button'));
          const signInButton = buttons.find(btn => 
            btn.textContent.toLowerCase().includes('sign in') || 
            btn.textContent.toLowerCase().includes('login')
          );
          if (signInButton) {
            signInButton.click();
          }
        });
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Check if modal appeared
        const modalCheck = await page.evaluate(() => {
          const modals = document.querySelectorAll('[role="dialog"], [class*="modal"], [class*="Modal"]');
          const visibleModals = Array.from(modals).filter(modal => {
            const style = window.getComputedStyle(modal);
            return style.display !== 'none' && style.visibility !== 'hidden' && modal.offsetParent !== null;
          });
          
          return {
            totalModals: modals.length,
            visibleModals: visibleModals.length,
            modalContent: visibleModals.length > 0 ? visibleModals[0].textContent.substring(0, 200) : ''
          };
        });
        
        console.log(`📋 Total modals: ${modalCheck.totalModals}`);
        console.log(`👁️ Visible modals: ${modalCheck.visibleModals}`);
        
        if (modalCheck.visibleModals > 0) {
          console.log('✅ Authentication modal opened successfully');
          console.log(`📝 Modal content preview: ${modalCheck.modalContent}...`);
          await page.screenshot({ path: 'auth-modal-opened.png', fullPage: false });
          console.log('📸 Screenshot saved: auth-modal-opened.png');
        } else {
          console.log('❌ Authentication modal not visible after click');
          await page.screenshot({ path: 'auth-modal-failed.png', fullPage: false });
          console.log('📸 Screenshot saved: auth-modal-failed.png');
        }
        
      } catch (error) {
        console.log(`❌ Error clicking Sign In button: ${error.message}`);
      }
    }
    
    console.log('\\n🔐 AUTHENTICATION MODAL TESTING COMPLETE');
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  
  await browser.close();
})();
